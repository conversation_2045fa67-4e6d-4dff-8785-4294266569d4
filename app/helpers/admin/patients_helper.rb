# frozen_string_literal: true

module Admin
  module <PERSON><PERSON><PERSON><PERSON><PERSON>
    def title_options_for_select
      ['', 'Mr', 'Mrs', 'Miss', 'Ms', 'Master', 'Dr', 'Sir']
    end

    def biological_sex_options_for_select
      ['', 'Male', 'Female']
    end

    def gender_options_for_select
      ['', 'Male', 'Female', 'Non-binary']
    end

    def pronouns_options_for_select(patient)
      ['', 'He/him', 'She/her', 'They/them', 'Ze/hir', 'Xe/xem', 'Ver/vir', 'Te/tem', 'E/em',
       patient.pronouns].compact.uniq
    end

    def tippy_payment_plans(patient)
      (patient.cot_payment_plans - patient.cot_payment_plans.first(2)).pluck(:name).join(', ')
    end

    def pinned_notes(patient)
      patient.patient_notes.where(archived: false, pinned: true).order(created_at: :desc)
    end

    def ethnicity_options_for_select
      ['',
       'African',
       'African American',
       'Albanian',
       'Anabaptist',
       'Arab',
       'Argentine',
       'Armenian',
       'Asian',
       'Austrian',
       'Bangladeshi',
       'Basque',
       'Belarusian',
       'Belgian',
       'Bosnian',
       'Brazilian',
       'British',
       'Bulgarian',
       'Cajun',
       'Cambodian',
       'Canadian',
       'Cape Verdean',
       'Caribbean',
       'Carpatho-Rusyn',
       'Chilean',
       'Chinese',
       'Croatian',
       'Cuban',
       'Czech',
       'Danish',
       'Dutch',
       'East Indian',
       'Ecuadorian',
       'Estonian',
       'Ethiopian',
       'Filipino',
       'Finnish',
       'French',
       'German',
       'Greek',
       'Haitian',
       'Hungarian',
       'Icelandic',
       'Indians of North America',
       'Irish',
       'Italian',
       'Japanese',
       'Jewish',
       'Korean',
       'Latin American',
       'Latvian',
       'Lebanese',
       'Lithuanian',
       'Luxembourger',
       'Macedonian',
       'Malaysian',
       'Maltese',
       'Mexican',
       'Middle Eastern',
       'Iranian',
       'Norwegian',
       'Pacific Islander',
       'Pakistani',
       'Palestinian',
       'Polish',
       'Portuguese',
       'Romanian',
       'Russian',
       'Scandinavian',
       'Scottish',
       'Serbian',
       'Slavic',
       'Slovak',
       'Slovenian',
       'Spanish',
       'Swedish',
       'Swiss',
       'Syrian',
       'Thai',
       'Turkish',
       'Ukranian',
       'Vietnamese',
       'Welsh',
       'West Indian',
       'Yugoslavian']
    end

    def age_in_years_and_months(date_of_birth)
      return 'Invalid date' unless date_of_birth.is_a?(Date)

      today = Time.zone.today

      years = today.year - date_of_birth.year
      months = today.month - date_of_birth.month

      months -= 1 if today.day < date_of_birth.day

      if months.negative?
        years -= 1
        months += 12
      end

      "#{years} #{'year'.pluralize(years)}, #{months} #{'month'.pluralize(months)}"
    end
  end
end
