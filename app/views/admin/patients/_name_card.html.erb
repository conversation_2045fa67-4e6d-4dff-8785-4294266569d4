<% alerts = patient.actions.where(action_type: "alert", completed: false) %>
<div class="w-fit d-flex gap-2 flex-column light-blue-bg" style="border-radius: 20px 0; padding: 12px 24px;" id="name_card">
  <div class="d-flex flex-row gap-3 align-items-center">
    <%= render 'layouts/shared/user_avatar', user: patient, width: 32, height: 32  %>
    <h3 id="patient_name">
      <span id="patient_full_name"><%= patient.full_name_with_title || 'New Patient' %></span>
      <span id="patient_pronouns"><%= patient.pronouns %></span>
      <% if alerts.count.positive? %>
       <a data-bs-toggle="modal" data-bs-target="#alertsModal" class="ms-2"><i class="fa-solid fa-triangle-exclamation"></i></a>
      <% end %>
    </h3>
  </div>
  <% if @patient.cot_payment_plans.count > 0 %>
    <div class="d-flex gap-3 payment-plan-badges">
      <% @patient.cot_payment_plans.first(2).each do |plan| %>
        <span class="badge rounded-pill border border-white text-white payment-plan-badge d-flex align-items-center cursor-pointer">
          <%= plan.name %>
        </span>
      <% end %>
      <% if @patient.cot_payment_plan_ids.count > 2 %>
        <span class="badge rounded-pill border border-white text-white payment-plan-badge cursor-pointer"
              data-tippy-content="<%= tippy_payment_plans(@patient) %>">
          +<%= @patient.cot_payment_plan_ids.count - 2 %>
        </span>
      <% end %>
    </div>
  <% end %>
</div>
<div style="width: 300px" class="mt-3 ms-3">
  <div id="pinned_notes"<% if pinned_notes(@patient).count < 2 %> class="d-block"<% end %>>
    <% pinned_notes(@patient).each do |note| %>
      <span class="badge border border-black pinned-note text-black rounded-pill cursor-pointer"
            style="font-size: 12px; font-weight: 500;"
            data-color="<%= note.color %>"
            data-id="<%= note.id %>">
        <%= note.title %>
      </span>
    <% end %>
  </div>
</div>
<div class="card h-100" style="border-radius: 0 20px; padding: 8px 32px;
  <% if @patient.invoices.where(due_date: ..Date.today).count > 0 %> background: #B9717233;
  <% elsif @patient.invoices.where(status: "unpaid").count > 0 %> background: rgb(247, 227, 209);
  <% else %> background: #BDCFDB;
  <% end %>">
  <div class="card-body p-0 d-flex justify-content-center align-items-center gap-3">
    <p class="mb-0" style="font-size: 16px;">Balance</p>
    <p class="mb-0" style="font-size: 16px; font-weight: 600;<% if @patient.invoices.where(due_date: ..Date.today).count > 0 || @patient.invoices.where(status: "unpaid").count > 0 %> color: #B97172;
      <% end %>"><%= number_to_currency(@patient.balance, unit: "£") %></p>
    <% if @patient.invoices.where(due_date: ..Date.today).count > 0 %>
      <span class="badge deep-red-bg rounded-pill border border-white" style="font-size: 14px;font-weight: 300;">Overdue</span>
    <% elsif @patient.invoices.where(status: "unpaid").count > 0 %>
      <span class="badge peach-orange-bg rounded-pill border border-black text-black" style="font-size: 14px;font-weight: 300;">Outstanding</span>
    <% else %>
      <span class="badge charcoal-blue-bg rounded-pill border border-white text-white" style="font-size: 14px;font-weight: 300;">Balance Clear</span>
    <% end %>
  </div>
</div>

<%= render "admin/patients/alerts_modal", alerts: alerts %>
