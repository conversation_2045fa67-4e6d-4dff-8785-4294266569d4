<div class="p-4">
  <% if @patient.temporary? %>
  <div class="mb-4 bg-amber-50 border border-amber-200 rounded-lg p-3 shadow-sm temporary-patient-banner">
    <div class="flex items-center">
      <div class="flex-shrink-0 mr-3">
        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="text-amber-500 w-5 h-5"><path d="M10.29 3.86L1.82 18a2 2 0 0 0 1.71 3h16.94a2 2 0 0 0 1.71-3L13.71 3.86a2 2 0 0 0-3.42 0z"></path><line x1="12" y1="9" x2="12" y2="13"></line><line x1="12" y1="17" x2="12.01" y2="17"></line></svg>
      </div>
      <div>
        <p class="text-sm font-medium text-amber-800">Temporary Patient</p>
        <p class="text-xs text-amber-700">This is a temporary patient record. It will be automatically deleted after 15 minutes if not completed. Please fill in the required information and save to keep this record.</p>
      </div>
    </div>
  </div>
  <% end %>
  <div class=" w-full bg-white  rounded-xl shadow-sm overflow-hidden flex flex-col border border-[#e5e5e5] ">
    <div class="relative px-6 py-2 flex items-center justify-between border-b border-[#e5e5e5]  bg-gradient-to-b from-white to-[#f9f9f9]   shadow-sm">
      <div class="absolute top-0 left-0 right-0 h-0.5 bg-gradient-to-r from-transparent via-[#0071e3]/20 to-transparent"></div>
      <div class="flex items-center gap-3">
        <!-- Avatar upload form -->
        <form action="<%= admin_patient_path(@patient) %>" method="post" enctype="multipart/form-data" data-patient-image-form class="relative">
          <%= hidden_field_tag :authenticity_token, form_authenticity_token %>
          <%= hidden_field_tag :_method, :patch %>
          <input type="file" name="patient[image]" id="patient_image_<%= @patient.id %>" accept="image/*" class="hidden" data-patient-image-input />

          <div class="relative inline-block" data-avatar-wrapper>
            <div class="block h-10 w-10 rounded-full overflow-hidden shadow-sm ring-2 ring-white/80">
              <% if @patient.image.attached? %>
                <%= image_tag @patient.image.url, alt: 'Patient photo', class: 'object-cover w-full h-full' %>
              <% else %>
                <div class="flex-shrink-0 h-full w-full bg-gray-200 rounded-full flex items-center justify-center">
                  <i class="fa-solid fa-user text-gray-400 text-xl"></i>
                </div>
              <% end %>
            </div>
            <label for="patient_image_<%= @patient.id %>" class="absolute -bottom-1 -right-1 cursor-pointer" data-patient-image-trigger>
              <% if @patient.image.attached? %>
                <span class="inline-flex items-center justify-center h-5 w-5 p-0 rounded-full bg-blue-100 text-blue-600 border border-blue-200 hover:bg-blue-200 hover:text-accent-foreground shadow">
                  <i class="fa-solid fa-pen text-[10px] pointer-events-none"></i>
                </span>
              <% else %>
                <span class="inline-flex items-center justify-center h-5 w-5 p-0 rounded-full bg-blue-100 text-blue-600 border border-blue-200 hover:bg-blue-200 hover:text-accent-foreground shadow">
                  <i class="fa-solid fa-plus text-[10px] pointer-events-none"></i>
                </span>
              <% end %>
            </label>
          </div>
        </form>
        <div class="flex flex-col">
          <div class="flex items-center gap-2">
            <h2 class="text-lg font-medium text-[#1d1d1f] tracking-tight patient-header-name"><%= @patient.full_name_with_title || 'New Patient' %> <span class="text-sm font-normal text-gray-500"><%= @patient.pronouns %></span></h2>
            <div class="payment-plan-badges flex items-center gap-1">
              <% if @patient.cot_payment_plans.present? %>
                <% @patient.cot_payment_plans.first(2).each do |plan| %>
                  <span class="px-2 py-0.5 text-xs font-medium bg-gradient-to-r from-[#e8f5f0] to-[#daf0e7] text-[#34a67f] rounded-md shadow-sm"><%= plan.name %></span>
                <% end %>
                <% if @patient.cot_payment_plan_ids.count > 2 %>
                  <span class="px-1.5 py-0.5 text-xs font-medium bg-gradient-to-r from-[#e8f5f0] to-[#daf0e7] text-[#34a67f] rounded-md shadow-sm" data-tippy-content="<%= tippy_payment_plans(@patient) %>">+<%= @patient.cot_payment_plan_ids.count - 2 %></span>
                <% end %>
              <% end %>
            </div>
            
            <!-- Vertical divider -->
            <div class="mx-2 h-4 border-l border-gray-300"></div>
                       
            <!-- Alerts badges -->
            <div class="alerts-badges flex items-center gap-1 ml-1">
              <% if @patient.alerts.present? %>
                <% @patient.alerts.first(2).each do |alert| %>
                  <span class="px-2 py-0.5 text-xs font-medium bg-gradient-to-r from-[#fde8e8] to-[#f8d4d4] text-[#e53e3e] rounded-md shadow-sm flex items-center">
                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-alert-triangle h-3 w-3 mr-1"><path d="m21.73 18-8-14a2 2 0 0 0-3.48 0l-8 14A2 2 0 0 0 4 21h16a2 2 0 0 0 1.73-3Z"/><path d="M12 9v4"/><path d="M12 17h.01"/></svg>
                    <%= alert.name %>
                  </span>
                <% end %>
                <% if @patient.alerts.count > 2 %>
                  <span class="px-1.5 py-0.5 text-xs font-medium bg-gradient-to-r from-[#fde8e8] to-[#f8d4d4] text-[#e53e3e] rounded-md shadow-sm" data-tippy-content="<%= @patient.alerts.map(&:name).join(', ') %>">+<%= @patient.alerts.count - 2 %></span>
                <% end %>
              <% end %>
            </div>
          </div>
          <p class="text-xs text-[#6e6e73] patient-header-dob">DOB: <%= @patient.date_of_birth&.strftime("%d/%m/%Y") %> • <%= @patient.age_in_years_and_months if @patient.date_of_birth.present? %></p>
        </div>
      </div>
      <div class="flex items-center gap-3">
        <div class="text-right bg-white/50 px-3 py-1 rounded-lg backdrop-blur-sm">
          <span class="text-xs text-[#6e6e73]">Balance</span>
          <span class="text-base font-medium <%= @patient.invoices.not_paid.where(due_date: ..Date.today).count > 0 || @patient.invoices.where(status: "unpaid").count > 0 ? 'text-red-600' : 'text-[#1d1d1f]' %> ml-2 tracking-tight"><%= number_to_currency(@patient.balance, unit: "£") %></span>
        </div>
        <% if @patient.invoices.not_paid.where(due_date: ..Date.today).count > 0 %>
          <div class="flex items-center gap-1.5 px-3.5 py-1.5 rounded-full bg-gradient-to-b from-[#fde8e8] to-[#f8d4d4] text-[#e53e3e] text-xs font-medium border border-[#f8d4d4]/40 shadow-sm cursor-default select-none">
            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-alert-circle h-3.5 w-3.5 mr-0.5"><circle cx="12" cy="12" r="10"/><line x1="12" y1="8" x2="12" y2="12"/><line x1="12" y1="16" x2="12.01" y2="16"/></svg>
            <span class="tracking-tight">Overdue</span>
          </div>
        <% elsif @patient.invoices.where(status: "unpaid").count > 0 %>
          <div class="flex items-center gap-1.5 px-3.5 py-1.5 rounded-full bg-gradient-to-b from-[#fff9eb] to-[#fff5d6] text-[#b07d1a] text-xs font-medium border border-[#fff0c7]/40 shadow-sm cursor-default select-none">
            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-alert-triangle h-3.5 w-3.5 mr-0.5"><path d="M21.73 18-8-14a2 2 0 0 1 3.48 0l-8 14A2 2 0 0 0 4 21h16a2 2 0 0 0 1.73-3Z"/><path d="M12 9v4"/><path d="M12 17h.01"/></svg>
            <span class="tracking-tight">Unpaid</span>
          </div>
        <% else %>
          <div class="flex items-center gap-1.5 px-3.5 py-1.5 rounded-full bg-gradient-to-b from-[#e8f5f0] to-[#d5ede5] text-[#34a67f] text-xs font-medium border border-[#c5e6d8]/40 shadow-sm cursor-default select-none">
            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-circle-check-big h-3.5 w-3.5 mr-0.5"><path d="M22 11.08V12a10 10 0 1 1-5.93-9.14"/><path d="m9 11 3 3L22 4"/></svg>
            <span class="tracking-tight">Balance Clear</span>
          </div>
        <% end %>
      </div>
    </div>
    <div class="bg-[#e1e1e3]  px-6 py-2 flex items-center gap-3 overflow-x-auto hide-scrollbar">

    </div>

    <div class="border-b border-[#e5e5e5]  rounded-b-xl overflow-hidden">
      <div class="flex items-center justify-between overflow-x-auto hide-scrollbar px-6">
        <div class="flex overflow-x-auto hide-scrollbar">
          <button class="px-4 py-2 text-sm font-medium transition-colors relative cursor-pointer <%= request.path.end_with?("/#{@patient.id}") ? 'text-[#0071e3] after:absolute after:bottom-0 after:left-0 after:right-0 after:h-0.5 after:bg-[#0071e3] after:rounded-t-full' : 'text-[#6e6e73] hover:text-[#1d1d1f]  ' %>" onclick="window.location.href='<%= admin_patient_path(@patient) if @patient.id.present? %>'">
            Information
          </button>
          <button class="px-4 py-2 text-sm font-medium transition-colors relative cursor-pointer <%= request.path.end_with?('/conversation') ? 'text-[#0071e3] after:absolute after:bottom-0 after:left-0 after:right-0 after:h-0.5 after:bg-[#0071e3] after:rounded-t-full' : 'text-[#6e6e73] hover:text-[#1d1d1f]  ' %>" onclick="window.location.href='<%= conversation_admin_patient_path(@patient) if @patient.id.present? %>'">
            Conversations
          </button>
          <button class="px-4 py-2 text-sm font-medium transition-colors relative cursor-pointer <%= request.path.end_with?('/assets') ? 'text-[#0071e3] after:absolute after:bottom-0 after:left-0 after:right-0 after:h-0.5 after:bg-[#0071e3] after:rounded-t-full' : 'text-[#6e6e73] hover:text-[#1d1d1f]  ' %>" onclick="window.location.href='<%= assets_admin_patient_path(@patient) if @patient.id.present? %>'">
            Assets
          </button>
          <button class="px-4 py-2 text-sm font-medium transition-colors relative cursor-pointer <%= request.path.end_with?('/charting_appointments') ? 'text-[#0071e3] after:absolute after:bottom-0 after:left-0 after:right-0 after:h-0.5 after:bg-[#0071e3] after:rounded-t-full' : 'text-[#6e6e73] hover:text-[#1d1d1f]  ' %>" onclick="window.location.href='<%= admin_patient_charting_appointments_path(@patient) if @patient.id.present? %>'">
            Appointments
          </button>
          <button class="px-4 py-2 text-sm font-medium transition-colors relative cursor-pointer <%= request.path.end_with?('/charting') ? 'text-[#0071e3] after:absolute after:bottom-0 after:left-0 after:right-0 after:h-0.5 after:bg-[#0071e3] after:rounded-t-full' : 'text-[#6e6e73] hover:text-[#1d1d1f]  ' %>" onclick="window.location.href='<%= @patient.id ? admin_patient_charting_path(@patient.id) : '' %>'" id="charting-tab">
            Charting
          </button>
          <button class="px-4 py-2 text-sm font-medium transition-colors relative cursor-pointer <%= request.path.end_with?('/lab_works') ? 'text-[#0071e3] after:absolute after:bottom-0 after:left-0 after:right-0 after:h-0.5 after:bg-[#0071e3] after:rounded-t-full' : 'text-[#6e6e73] hover:text-[#1d1d1f]  ' %>" onclick="window.location.href='<%= @patient.id ? admin_patient_lab_works_path(@patient.id) : '' %>'" id="lab-works-tab">
            Lab Work
          </button>
          <button class="px-4 py-2 text-sm font-medium transition-colors relative cursor-pointer <%= request.path.end_with?('/recalls') ? 'text-[#0071e3] after:absolute after:bottom-0 after:left-0 after:right-0 after:h-0.5 after:bg-[#0071e3] after:rounded-t-full' : 'text-[#6e6e73] hover:text-[#1d1d1f]  ' %>" onclick="window.location.href='<%= @patient.id ? admin_patient_recalls_path(@patient.id) : '' %>'" id="recalls-tab">
            Recalls
          </button>
          <button class="px-4 py-2 text-sm font-medium transition-colors relative cursor-pointer <%= request.path.end_with?('/medical_histories') ? 'text-[#0071e3] after:absolute after:bottom-0 after:left-0 after:right-0 after:h-0.5 after:bg-[#0071e3] after:rounded-t-full' : 'text-[#6e6e73] hover:text-[#1d1d1f]  ' %>" onclick="window.location.href='<%= @patient.id ? admin_patient_medical_histories_path(@patient.id) : '' %>'" id="medical-history-tab">
            Medical History
          </button>
          <button class="px-4 py-2 text-sm font-medium transition-colors relative cursor-pointer <%= request.path.end_with?('/prescriptions') ? 'text-[#0071e3] after:absolute after:bottom-0 after:left-0 after:right-0 after:h-0.5 after:bg-[#0071e3] after:rounded-t-full' : 'text-[#6e6e73] hover:text-[#1d1d1f]  ' %>" onclick="window.location.href='<%= @patient.id ? prescriptions_admin_patient_path(@patient) : '' %>'">
            Prescriptions
          </button>
          <button class="px-4 py-2 text-sm font-medium transition-colors relative cursor-pointer <%= request.path.end_with?('/account') ? 'text-[#0071e3] after:absolute after:bottom-0 after:left-0 after:right-0 after:h-0.5 after:bg-[#0071e3] after:rounded-t-full' : 'text-[#6e6e73] hover:text-[#1d1d1f]  ' %>" onclick="window.location.href='<%= @patient.id ? admin_patient_account_path(@patient.id) : '' %>'" id="account-tab">
            Account
          </button>

          <button class="px-4 py-2 text-sm font-medium transition-colors relative cursor-pointer <%= request.path.end_with?('/letters') ? 'text-[#0071e3] after:absolute after:bottom-0 after:left-0 after:right-0 after:h-0.5 after:bg-[#0071e3] after:rounded-t-full' : 'text-[#6e6e73] hover:text-[#1d1d1f]  ' %>" onclick="window.location.href='<%= @patient.id ? admin_patient_letters_path(@patient.id) : '' %>'" id="letters-tab">
            Correspondence
          </button>

          <button class="px-4 py-2 text-sm font-medium transition-colors relative cursor-pointer <%= request.path.end_with?('/actions') ? 'text-[#0071e3] after:absolute after:bottom-0 after:left-0 after:right-0 after:h-0.5 after:bg-[#0071e3] after:rounded-t-full' : 'text-[#6e6e73] hover:text-[#1d1d1f]  ' %>" onclick="window.location.href='<%= @patient.persisted? ? actions_admin_patient_path(@patient) : '' %>'">
            Actions
          </button>
          <button class="px-4 py-2 text-sm font-medium transition-colors relative cursor-pointer <%= request.path.end_with?('/event_logs') ? 'text-[#0071e3] after:absolute after:bottom-0 after:left-0 after:right-0 after:h-0.5 after:bg-[#0071e3] after:rounded-t-full' : 'text-[#6e6e73] hover:text-[#1d1d1f]  ' %>" onclick="window.location.href='<%= @patient.persisted? ? admin_patient_event_logs_path(@patient) : '' %>'" id="audit-tab">
            Audit
          </button>
        </div>

        <% pinned_notes_filtered = pinned_notes(@patient) %>
        <% has_pinned_notes = pinned_notes_filtered.any? %>
        <% is_information_page = request.path.end_with?("/#{@patient.id}") %>
        <% should_auto_expand = has_pinned_notes && !is_information_page %>
        <div class="relative notes-dropdown-container flex items-center my-1" data-patient-id="<%= @patient.id %>">

          <div class="relative login-as-patient-container mr-2">
            <button type="button"
              class="login-as-patient-trigger group flex items-center gap-2 px-3 py-1.5 rounded-full transition-all duration-300 bg-gradient-to-b from-white to-[#f5f7fa] border border-[#e5e5e5]/80 shadow-[0_1px_2px_rgba(0,0,0,0.05)] hover:shadow-[0_2px_4px_rgba(0,0,0,0.08)] backdrop-blur-sm hover:ring-1 hover:ring-[#0071e3]/20"
              data-patient-id="<%= @patient.id %>"
              id="open-device-selection-modal"
              data-patient-id="<%= @patient.id %>"
              onclick="if (window.initializePusherForLogin) { initializePusherForLogin(); } event.preventDefault(); sendLoginAsPatientNotification(<%= @patient.id %>); return false;">
              <span class="text-xs font-medium text-[#1d1d1f] tracking-tight">Login as Patient</span>
              <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-log-in h-3.5 w-3.5 text-[#8e8e93] transition-all duration-300 group-hover:text-[#6e6e73]"><path d="M15 3h4a2 2 0 0 1 2 2v14a2 2 0 0 1-2 2h-4"/><polyline points="10 17 15 12 10 7"/><line x1="15" x2="3" y1="12" y2="12"/></svg>
            </button>
          </div>

          <button type="button" aria-expanded="<%= should_auto_expand ? 'true' : 'false' %>" aria-haspopup="true"
            class="notes-dropdown-trigger group flex items-center gap-2 px-3 py-1.5 rounded-full transition-all duration-300 bg-gradient-to-b from-white to-[#f5f7fa] dark:from-[#3a3a3c] dark:to-[#323234] border border-[#e5e5e5]/80 dark:border-[#4c4c4e]/50 shadow-[0_1px_2px_rgba(0,0,0,0.05)] hover:shadow-[0_2px_4px_rgba(0,0,0,0.08)] backdrop-blur-sm hover:ring-1 hover:ring-[#0071e3]/20">
            <span class="text-xs font-medium text-[#1d1d1f] dark:text-white tracking-tight">Notes</span>
            <div class="flex items-center justify-center h-4 w-4 rounded-full text-xs font-medium transition-all bg-gradient-to-b from-[#0077ed] to-[#0071e3] text-white shadow-[0_1px_2px_rgba(0,0,0,0.1)] group-hover:from-[#0071e3] group-hover:to-[#006edb] group-active:scale-95">
              <%= pinned_notes_filtered.count %>
            </div>
            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-chevron-down h-3.5 w-3.5 text-[#8e8e93] transition-all duration-300 group-hover:text-[#6e6e73] dark:group-hover:text-[#a1a1a6]"><path d="m6 9 6 6 6-6"></path></svg>
          </button>
          <div class="notes-dropdown-menu fixed w-1/4 z-50 bg-white dark:bg-[#232325] border border-gray-200 dark:border-[#444] rounded-xl shadow-lg py-2 px-2 hidden">
              <div class="relative mute-notes-container flex justify-end mb-3">
                <button type="button" aria-expanded="false" aria-haspopup="true"
                  class="mute-notes-trigger group flex items-center gap-2 px-3 py-1.5 rounded-full transition-all duration-300 bg-gradient-to-b from-white to-[#f5f7fa] dark:from-[#3a3a3c] dark:to-[#323234] border border-[#e5e5e5]/80 dark:border-[#4c4c4e]/50 shadow-[0_1px_2px_rgba(0,0,0,0.05)] hover:shadow-[0_2px_4px_rgba(0,0,0,0.08)] backdrop-blur-sm hover:ring-1 hover:ring-[#0071e3]/20">
                  <span class="text-xs font-medium text-[#1d1d1f] dark:text-white tracking-tight">Mute</span>
                  <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-bell-off h-3.5 w-3.5 text-[#8e8e93] transition-all duration-300 group-hover:text-[#6e6e73] dark:group-hover:text-[#a1a1a6]"><path d="M8.7 3A6 6 0 0 1 18 8a21.3 21.3 0 0 0 .6 5"></path><path d="M17 17H3s3-2 3-9a4.67 4.67 0 0 1 .3-1.7"></path><path d="M10.3 21a1.94 1.94 0 0 0 3.4 0"></path><path d="m2 2 20 20"></path></svg>
                </button>
                <div class="mute-notes-menu fixed w-52 z-50 bg-white dark:bg-[#232325] border border-gray-200 dark:border-[#444] rounded-xl shadow-lg py-2 px-2 hidden" style="transform: translateX(-120px);">
                  <div class="text-xs font-medium text-gray-500 dark:text-gray-400 px-3 py-1">Mute notes for:</div>
                  <div class="mute-option cursor-pointer px-3 py-2 hover:bg-gray-100 dark:hover:bg-gray-800 rounded-lg transition-colors duration-150" data-mute-option="next-select">
                    <div class="text-sm font-medium text-gray-800 dark:text-gray-200">Until I next select this patient</div>
                  </div>
                  <div class="mute-option cursor-pointer px-3 py-2 hover:bg-gray-100 dark:hover:bg-gray-800 rounded-lg transition-colors duration-150" data-mute-option="15min">
                    <div class="text-sm font-medium text-gray-800 dark:text-gray-200">15 minutes</div>
                  </div>
                  <div class="mute-option cursor-pointer px-3 py-2 hover:bg-gray-100 dark:hover:bg-gray-800 rounded-lg transition-colors duration-150" data-mute-option="30min">
                    <div class="text-sm font-medium text-gray-800 dark:text-gray-200">30 minutes</div>
                  </div>
                  <div class="mute-option cursor-pointer px-3 py-2 hover:bg-gray-100 dark:hover:bg-gray-800 rounded-lg transition-colors duration-150" data-mute-option="1hour">
                    <div class="text-sm font-medium text-gray-800 dark:text-gray-200">1 hour</div>
                  </div>
                  <div class="mute-option cursor-pointer px-3 py-2 hover:bg-gray-100 dark:hover:bg-gray-800 rounded-lg transition-colors duration-150" data-mute-option="4hours">
                    <div class="text-sm font-medium text-gray-800 dark:text-gray-200">4 hours</div>
                  </div>
                  <div class="mute-option cursor-pointer px-3 py-2 hover:bg-gray-100 dark:hover:bg-gray-800 rounded-lg transition-colors duration-150" data-mute-option="tomorrow">
                    <div class="text-sm font-medium text-gray-800 dark:text-gray-200">Until tomorrow</div>
                  </div>
                </div>
              </div>
            <% if has_pinned_notes %>
              <% pinned_notes_filtered.each do |note| %>
                <div class="bg-<%= note.color %>-100 rounded-lg p-3 mb-2 relative border-0 shadow-sm hover:scale-[1.01] transition-transform duration-200" data-note-id="<%= note.id %>" style="box-shadow: rgba(<%= note.color == 'amber' ? '251, 191, 36' : note.color == 'blue' ? '59, 130, 246' : note.color == 'green' ? '34, 197, 94' : '244, 114, 182' %>, 0.1) 0px 10px 15px -3px, rgba(<%= note.color == 'amber' ? '251, 191, 36' : note.color == 'blue' ? '59, 130, 246' : note.color == 'green' ? '34, 197, 94' : '244, 114, 182' %>, 0.2) 0px 4px 6px -4px;">
                  <div class="absolute right-0 top-0 w-6 h-6 bg-<%= note.color %>-200/50 rounded-bl-xl" style="clip-path: polygon(100% 0px, 0px 0px, 100% 100%);"></div>
                  <div class="font-medium text-[14px] text-<%= note.color %>-900"><%= note.title.presence || 'Note' %></div>
                  <div class="text-[13px] text-<%= note.color %>-800"><%= note.text %></div>
                  <div class="mt-1 text-[11px] text-<%= note.color %>-700/70">Pinned <%= time_ago_in_words(note.updated_at) %> ago</div>
                </div>
              <% end %>
            <% else %>
              <div class="p-4 text-center text-gray-400 text-xs">No pinned notes</div>
            <% end %>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Device Selection Modal -->
  <div id="device-selection-modal" class="fixed inset-0 bg-black/50 flex items-center justify-center z-50 hidden">
    <div class="bg-white rounded-xl shadow-lg w-full max-w-md mx-auto">
      <div class="flex items-center justify-between p-4 border-b border-gray-200">
        <div class="flex items-center">
          <div class="w-8 h-8 rounded-full bg-sky-100 flex items-center justify-center mr-3">
            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="h-4 w-4 text-sky-600">
              <path d="M9.75 17L9 20l-1 1h8l-1-1-.75-3M3 13h18M5 17h14a2 2 0 002-2V5a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
            </svg>
          </div>
          <h3 class="text-lg font-medium text-gray-900">Select Device for Patient Login</h3>
        </div>
        <button id="close-device-modal" class="text-gray-400 hover:text-gray-500">
          <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-x h-5 w-5">
            <path d="M18 6 6 18"></path>
            <path d="m6 6 12 12"></path>
          </svg>
        </button>
      </div>

      <div class="p-4">
        <div class="form-group">
          <label class="text-[14px] font-medium text-gray-500 mb-2 block">Available Devices</label>

          <div id="device-list-container" class="mt-2 space-y-2 max-h-60 overflow-y-auto">
            <div class="text-center py-8" id="loading-devices">
              <div class="inline-block animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-sky-500"></div>
              <p class="mt-2 text-sm text-gray-500">Loading available devices...</p>
            </div>

            <div id="no-devices-message" class="hidden py-4 text-center">
              <p class="text-sm text-gray-500">No active devices found. Please register and activate devices in practice settings.</p>
            </div>

            <div id="device-list" class="hidden space-y-2"></div>
          </div>

          <p class="text-[12px] text-gray-400 mt-3 ml-1">Select a device to send the patient login notification to</p>
        </div>
      </div>

      <div class="flex justify-between p-4 border-t border-gray-200">
        <button id="cancel-device-selection" class="w-full px-4 py-2 text-sm font-medium border-gray-100 text-gray-500 hover:bg-gray-50/70 hover:text-gray-600 bg-white border rounded-xl h-11">
          Cancel
        </button>
      </div>
    </div>
  </div>
</div>
