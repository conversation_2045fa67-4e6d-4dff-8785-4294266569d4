<div class="flex-shrink-0 flex flex-col w-[280px] list-container" data-list-id="<%= list.id %>" data-board-id="<%= list.crm_board_id %>" data-is-completed-list="<%= list.is_completed_list? %>">
<div class="flex items-center justify-between mb-3 flex-shrink-0 relative list-drag-handle">
  <div class="flex items-center">
    <h3 class="font-medium text-[16px] list-title" data-list-id="<%= list.id %>" data-original-title="<%= list.title %>">
      <span class="list-title-text" <% if list.title.length > 20 %>title="<%= html_escape(list.title) %>"<% end %>><%= truncate(list.title, length: 20) %></span>
      <input type="text" class="list-title-input hidden w-full px-1 py-0.5 border border-blue-300 rounded focus:outline-none focus:ring-1 focus:ring-blue-500 text-[16px]" value="<%= list.title %>">
    </h3>
    <div class="ml-2 flex items-center justify-center w-5 h-5 rounded-full bg-[#e5e5ea] text-[12px]"><%= list.card_count %></div>

    <% if list.is_default? %>
      <span class="ml-2 text-[11px] px-2 py-0.5 rounded-full text-gray-700 bg-green-200">Default</span>
    <% end %>
    </div>
  <button class="text-gray-500 hover:text-gray-700 p-1 rounded-full hover:bg-gray-100 list-actions-toggle cursor-pointer">
    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-ellipsis w-5 h-5">
      <circle cx="12" cy="12" r="1"></circle>
      <circle cx="19" cy="12" r="1"></circle>
      <circle cx="5" cy="12" r="1"></circle>
    </svg>
  </button>

  <div class="absolute right-0 top-8 bg-white rounded-lg shadow-lg z-50 w-64 overflow-hidden list-actions-menu hidden">
    <div class="py-1 border-b border-gray-100">
      <button class="w-full px-4 py-3 text-left flex items-center text-[14px] hover:bg-gray-50 view-as-table-btn">
        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-table w-5 h-5 mr-3 text-gray-500"><path d="M12 3v18"></path><rect width="18" height="18" x="3" y="3" rx="2"></rect><path d="M3 9h18"></path><path d="M3 15h18"></path></svg>
        <span>View as table</span>
      </button>
    </div>
    <div class="py-1 border-b border-gray-100">
      <button class="w-full px-4 py-3 text-left flex items-center text-[14px] hover:bg-gray-50 text-blue-500 edit-title-btn">
        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-pencil w-5 h-5 mr-3 text-blue-500"><path d="M17 3a2.85 2.83 0 1 1 4 4L7.5 20.5 2 22l1.5-5.5Z"></path><path d="m15 5 4 4"></path></svg>
        <span>Edit title</span>
      </button>
    </div>
    <div class="py-1 border-b border-gray-100">
      <button class="w-full px-4 py-3 text-left flex items-center text-[14px] hover:bg-gray-50 edit-restrictions-btn">
        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-lock w-5 h-5 mr-3 text-gray-500"><rect width="18" height="11" x="3" y="11" rx="2" ry="2"></rect><path d="M7 11V7a5 5 0 0 1 10 0v4"></path></svg>
        <span>Edit restrictions</span>
      </button>
    </div>
    <div class="py-1 border-b border-gray-100">
      <button class="w-full px-4 py-3 text-left flex items-center text-[14px] hover:bg-gray-50 text-red-500 delete-list-btn">
        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-trash w-5 h-5 mr-3 text-red-500"><path d="M3 6h18"></path><path d="M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6"></path><path d="M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2"></path></svg>
        <span>Delete list</span>
      </button>
    </div>
    <div class="py-1">
      <button class="w-full px-4 py-3 text-left flex items-center text-[14px] hover:bg-gray-50 add-patient-btn">
        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-user-plus w-5 h-5 mr-3 text-gray-500"><path d="M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2"></path><circle cx="9" cy="7" r="4"></circle><line x1="19" x2="19" y1="8" y2="14"></line><line x1="22" x2="16" y1="11" y2="11"></line></svg>
        <span>Add patient</span>
      </button>
    </div>
  </div>
</div>
<% if list.has_summary? %>
  <%= render 'admin/crm/lists/summary_card', list: list %>
<% else %>
<div class="bg-white rounded-lg border border-gray-200 border-dashed p-4 mb-3 flex items-center justify-center">
  <button class="add-summary-btn flex items-center text-gray-500 text-[14px]" data-list-id="<%= list.id %>">
    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-plus w-4 h-4 mr-2">
      <path d="M5 12h14"></path>
      <path d="M12 5v14"></path>
    </svg>
    Add Summary
  </button>
</div>
<% end %>
  <div data-list-id="<%= list.id %>" class="flex-1 overflow-y-auto px-2 pb-4 min-h-[200px]" style="max-height: calc(-180px + 100vh);">
    <% if list.crm_cards.any? %>
      <% list.crm_cards.each do |card| %>
        <%= render 'admin/crm/cards/card', card: card %>
      <% end %>
    <% end %>
  </div>
<div class="mt-3 flex-shrink-0">
  <button class="w-full flex items-center justify-center py-2 text-[14px] text-gray-500 bg-white/50 rounded-xl hover:bg-white hover:shadow-sm transition-all toggle-add-card-btn" data-list-id="<%= list.id %>">
    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-plus w-4 h-4 mr-2">
      <path d="M5 12h14"></path>
      <path d="M12 5v14"></path>
    </svg>
    Add Patient
  </button>
</div>
</div>
