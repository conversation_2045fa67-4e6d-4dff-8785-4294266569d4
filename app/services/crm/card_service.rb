# frozen_string_literal: true

module Crm
  class CardService
    def self.create_card(list_id, params)
      @creating_card ||= {}

      return @creating_card[list_id] if @creating_card[list_id]

      @creating_card[list_id] = :in_progress

      begin
        list = CrmList.find(list_id)

        params[:position] = list.crm_cards.maximum(:position).to_i + 1 unless params[:position]

        if params[:patient_id].present?
          practice_id = list.crm_board.practice_id

          patient = Patient.joins(:practices_patients)
                           .where(id: params[:patient_id], practices_patients: { practice_id: practice_id })
                           .first

          if patient
            params[:title] = patient.full_name

            set_last_contacted_from_conversation(patient, params)
          else
            card = CrmCard.new
            card.errors.add(:base, 'Patient does not belong to this practice')
            return card
          end
        end

        card_params = params.except(:practice_id)
        card = list.crm_cards.new(card_params)

        card.audit ||= []
        card.audit << {
          action: 'created',
          time: Time.current.iso8601,
          text: 'Card created'
        }

        if card.patient_id.present? && card.patient
          card.audit << {
            action: 'associated',
            time: Time.current.iso8601,
            text: "Associated with patient: #{card.patient.full_name}"
          }
        end

        card.save
        @creating_card[list_id] = card
        card
      ensure
        @creating_card&.delete(list_id)
      end
    rescue ActiveRecord::RecordNotFound
      card = CrmCard.new
      card.errors.add(:base, 'List not found')
      card
    rescue StandardError
      card = CrmCard.new
      card.errors.add(:base, 'Error creating card')
      card
    end

    def self.update_card(card_id, params)
      card = CrmCard.find(card_id)

      old_title = card.title
      old_desc = card.description || ''
      old_labels = card.labels || []
      old_patient_id = card.patient_id

      if params[:patient_id].present? && params[:patient_id].to_i != old_patient_id
        patient = Patient.find(params[:patient_id])
        params[:title] = patient.full_name if patient
      end

      card_params = params.except(:practice_id)

      card.update(card_params)

      card.audit ||= []

      changes = []
      changes << "Title updated from \"#{old_title}\" to \"#{card.title}\"" if params[:title] && old_title != card.title
      changes << 'Description updated' if params[:description] && old_desc != card.description
      changes << 'Labels updated' if params[:labels] && old_labels != card.labels

      if params[:patient_id].present? && old_patient_id != card.patient_id
        if old_patient_id.present?
          old_patient = Patient.find_by(id: old_patient_id)
          old_patient_name = old_patient ? old_patient.full_name : 'Unknown'
          changes << "Patient association changed from \"#{old_patient_name}\" to \"#{card.patient.full_name}\""
        else
          changes << "Associated with patient: #{card.patient.full_name}"
        end
      elsif params[:patient_id] == '' && old_patient_id.present?
        old_patient = Patient.find_by(id: old_patient_id)
        old_patient_name = old_patient ? old_patient.full_name : 'Unknown'
        changes << "Patient association removed (was: \"#{old_patient_name}\")"
      end

      if changes.any?
        card.audit << {
          action: 'edited',
          time: Time.current.iso8601,
          text: changes.join(', ')
        }
        card.save
      end

      card
    rescue ActiveRecord::RecordNotFound
      card = CrmCard.new
      card.errors.add(:base, 'Card not found')
      card
    rescue StandardError
      card.errors.add(:base, 'Error updating card')
      card
    end

    def self.update_treatment(card_id, treatment_id)
      card = CrmCard.find(card_id)
      card.treatment_id = treatment_id
      if card.save
        treatment = Treatment.find(treatment_id)
        CardActivity.log_activity(
          card: card,
          user: Current.user,
          activity_type: 'updated',
          description: "Treatment changed to '",
          metadata: {
            treatment_id: treatment.id,
            treatment_name: treatment.patient_friendly_name
          }
        )
      end
      card
    rescue ActiveRecord::RecordNotFound
      card = CrmCard.new
      card.errors.add(:base, 'Card or Treatment not found')
      card
    end

    def self.delete_card(card_id)
      card = CrmCard.find(card_id)
      card.destroy
      card
    rescue ActiveRecord::RecordNotFound
      card = CrmCard.new
      card.errors.add(:base, 'Card not found')
      card
    rescue StandardError
      card.errors.add(:base, 'Error deleting card')
      card
    end

    def self.move_card(card_id, target_list_id, position = nil)
      card = CrmCard.find(card_id)
      source_list = card.crm_list
      target_list = target_list_id.present? ? CrmList.find(target_list_id) : source_list
      can_move = if target_list_id.present?
                   target_list.crm_list_restrictions.all? do |restriction|
                     restriction.check_card(card, source_list)
                   end
                 else
                   true
                 end

      unless can_move
        card.errors.add(:base, 'Card does not meet target list restrictions')
        return card
      end

      begin
        ActiveRecord::Base.transaction do
          if source_list.id != target_list.id
            card.audit ||= []
            card.audit << {
              action: 'moved',
              time: Time.current.iso8601,
              text: "Moved from \"#{source_list.title}\" to \"#{target_list.title}\""
            }
          end

          card.position = position if position
          card.crm_list = target_list
          card.save!
        end
      rescue ActiveRecord::RecordInvalid => e
        card.errors.add(:base, "Error saving card: #{e.message}")
      rescue StandardError => e
        card.errors.add(:base, "Error moving card: #{e.message}")
      end

      card
    rescue ActiveRecord::RecordNotFound
      card = CrmCard.new
      card.errors.add(:base, 'Card or list not found')
      card
    rescue StandardError => e
      card ||= CrmCard.new
      card.errors.add(:base, "Error moving card: #{e.message}")
      card
    end

    def self.move_to_board(card_id, target_board_id, target_list_id = nil)
      card = CrmCard.find(card_id)
      source_board = card.crm_list.crm_board
      target_board = CrmBoard.find(target_board_id)

      unless source_board.practice_id == target_board.practice_id
        card.errors.add(:base, 'Cannot move card to a board from a different practice')
        return card
      end
      target_list = target_list_id ? CrmList.find(target_list_id) : target_board.crm_lists.first

      can_move = target_list.crm_list_restrictions.all? { |restriction| restriction.check_card(card, card.crm_list) }

      unless can_move
        card.errors.add(:base, 'Card does not meet target list restrictions')
        return card
      end

      ActiveRecord::Base.transaction do
        card.audit ||= []
        card.audit << {
          action: 'moved',
          time: Time.current.iso8601,
          text: "Moved from board \"#{source_board.name}\" to \"#{target_board.name}\""
        }

        transfer_service = CustomFieldTransferService.new(source_board, target_board)
        transfer_service.transfer_custom_fields_for_card(card)

        card.crm_list = target_list
        card.save
      rescue StandardError
        card.errors.add(:base, 'Error moving card')
        raise ActiveRecord::Rollback
      end

      card
    rescue ActiveRecord::RecordNotFound
      card = CrmCard.new
      card.errors.add(:base, 'Card, board, or list not found')
      card
    rescue StandardError
      card.errors.add(:base, 'Error moving card to board')
      card
    end

    def self.set_last_contacted_from_conversation(patient, params)
      return if patient.linked_conversation.blank?

      conversation = patient.linked_conversation
      last_message = conversation.conversation_messages.order(created_at: :desc).first

      params[:last_contacted_at] = last_message.created_at if last_message.present?
    end
  end
end
