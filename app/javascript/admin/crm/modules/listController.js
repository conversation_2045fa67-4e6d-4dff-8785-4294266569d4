// List Controller Module
import utils from './utils';
import modalUtils from '../modal';
import patientController from './patientController';

const listController = {
  initEventListeners(listElement) {
    this.initActionsMenu(listElement);
    this.initEditForm(listElement);
    this.initEditRestrictions(listElement);
    this.initDeleteButton(listElement);
    this.initAddCardButton(listElement);
    this.initCancelAddCard(listElement);
  },

  initActionsMenu(listElement) {
    const toggleButton = listElement.querySelector('.list-actions-toggle');
    const actionsMenu = listElement.querySelector('.list-actions-menu');
    
    if (!toggleButton || !actionsMenu) {
      return;
    }
    
    // Toggle menu visibility when clicking the button
    toggleButton.addEventListener('click', (e) => {
      e.preventDefault();
      e.stopPropagation();
      actionsMenu.classList.toggle('hidden');
      
      // Add a one-time event listener to close the menu when clicking outside
      const closeMenu = (event) => {
        if (!actionsMenu.contains(event.target) && event.target !== toggleButton) {
          actionsMenu.classList.add('hidden');
          document.removeEventListener('click', closeMenu);
        }
      };
      
      if (!actionsMenu.classList.contains('hidden')) {
        // Wait for the next tick to add the event listener
        setTimeout(() => {
          document.addEventListener('click', closeMenu);
        }, 0);
      }
    });
    
    // View as table button
    const viewAsTableBtn = actionsMenu.querySelector('.view-as-table-btn');
    if (viewAsTableBtn) {
      viewAsTableBtn.addEventListener('click', () => {
        const listId = listElement.dataset.listId;
        const boardId = listElement.dataset.boardId;
        if (listId && boardId) {
          window.location.href = `/admin/crm/boards/${boardId}/lists/${listId}/table`;
        }
        actionsMenu.classList.add('hidden');
      });
    }
    
    // Edit title button
    const editTitleBtn = actionsMenu.querySelector('.edit-title-btn');
    if (editTitleBtn) {
      editTitleBtn.addEventListener('click', () => {
        const listId = listElement.dataset.listId;
        const titleElement = listElement.querySelector('h3');
        const currentTitle = titleElement.dataset.originalTitle || titleElement.textContent.trim();
        
        if (listId) {
          this.showEditTitleModal(listId, currentTitle);
        }
        actionsMenu.classList.add('hidden');
      });
    }
    
    // Edit restrictions button
    const editRestrictionsBtn = actionsMenu.querySelector('.edit-restrictions-btn');
    if (editRestrictionsBtn) {
      editRestrictionsBtn.addEventListener('click', () => {
        const listId = listElement.dataset.listId;
        if (listId) {
          this.showEditRestrictionsModal(listId);
        }
        actionsMenu.classList.add('hidden');
      });
    }
    
    // Delete list button
    const deleteListBtn = actionsMenu.querySelector('.delete-list-btn');
    if (deleteListBtn) {
      deleteListBtn.addEventListener('click', () => {
        const listId = listElement.dataset.listId;
        if (listId) {
          this.confirmDeleteList(listId);
        }
        actionsMenu.classList.add('hidden');
      });
    }
    
    // Add patient button
    const addPatientBtn = actionsMenu.querySelector('.add-patient-btn');
    if (addPatientBtn) {
      addPatientBtn.addEventListener('click', () => {
        const listId = listElement.dataset.listId;
        if (listId) {
          patientController.showSearchModal(listId);
        }
        actionsMenu.classList.add('hidden');
      });
    }
  },
  
  showEditTitleModal(listId, currentTitle) {
    modalUtils.showModal({
      title: 'Edit List Title',
      content: `
        <div class="form-group">
          <label for="list-title" class="text-[14px] font-medium text-gray-500">List Title</label>
          <input id="list-title" type="text" class="flex w-full border py-2 ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium file:text-foreground placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 rounded-xl border-gray-200/80 h-11 px-4 focus-visible:ring-gray-300 text-[14px] bg-gray-50/30 backdrop-blur-sm" value="${currentTitle}" placeholder="Enter list title" required>
          <p class="input-error text-[12px] text-red-500 mt-1 ml-1 hidden">Please enter a list title</p>
        </div>
      `,
      showCancelButton: true,
      confirmButtonText: 'Save',
      cancelButtonText: 'Cancel',
      confirmButtonColor: 'bg-sky-300 hover:bg-sky-400',
      cancelButtonColor: 'border-gray-100 text-gray-500 hover:bg-gray-50/70 hover:text-gray-600',
      html: true,
      focusConfirm: false,
      preConfirm: () => {
        const title = document.getElementById('list-title').value;
        const errorEl = document.querySelector('.input-error');
        if (!title.trim()) {
          if (errorEl) {
            errorEl.textContent = 'Please enter a list title';
            errorEl.classList.remove('hidden');
          }
          return false;
        }
        if (errorEl) errorEl.classList.add('hidden');
        return { title };
      }
    }).then((result) => {
      if (result.isConfirmed) {
        // Get the title directly from the input field since result.value might not exist
        const titleInput = document.getElementById('list-title');
        const newTitle = titleInput ? titleInput.value.trim() : currentTitle;
        
        if (newTitle) {
          this.updateListTitle(listId, newTitle);
        }
      }
    });
  },
  
  getBoardId() {
    // Try to get board ID from URL first
    const urlMatch = window.location.pathname.match(/\/admin\/crm\/boards\/(\d+)/);
    if (urlMatch && urlMatch[1]) {
      return urlMatch[1];
    }
    
    // If not in URL, try to get from data attribute on the board element
    const boardElement = document.querySelector('[data-board-id]');
    if (boardElement && boardElement.dataset.boardId) {
      return boardElement.dataset.boardId;
    }
    
    return null;
  },
  
  async updateListTitle(listId, title) {
    try {
      // Get the CSRF token
      const csrfToken = document.querySelector('meta[name="csrf-token"]').content;
      
      // Use fetch to call the update_title endpoint
      const response = await fetch(`/admin/crm/lists/${listId}/update_title`, {
        method: 'PATCH',
        headers: {
          'X-CSRF-Token': csrfToken,
          'Content-Type': 'application/json',
          'Accept': 'application/json'
        },
        body: JSON.stringify({
          crm_list: {
            title: title
          }
        })
      });
      
      if (!response.ok) {
        throw new Error(`HTTP error! Status: ${response.status}`);
      }
      
      const data = await response.json();
      
      if (data.success) {
        // Update the title in the DOM
        const listElement = document.querySelector(`.list-container[data-list-id="${listId}"]`);
        if (listElement) {
          const titleElement = listElement.querySelector('h3');
          const titleTextElement = listElement.querySelector('.list-title-text');
          if (titleElement && titleTextElement) {
            // Update the data attribute with the full title
            titleElement.dataset.originalTitle = title;

            // Update the displayed text (truncated if necessary)
            const truncatedTitle = title.length > 20 ? title.substring(0, 20) + '...' : title;
            titleTextElement.textContent = truncatedTitle;

            // Update or add tooltip if title is truncated
            if (title.length > 20) {
              titleTextElement.setAttribute('title', title);
            } else {
              titleTextElement.removeAttribute('title');
            }
          }
        }
        
        // Show success message
        toastr.success('List title updated successfully', 'Success!');
      } else {
        throw new Error(data.errors || 'Unknown error');
      }
    } catch (error) {
      console.error('Error updating list title:', error);
      toastr.error(error.message || 'An error occurred', 'Error');
    }
  },
  
  showEditRestrictionsModal(listId) {
    modalUtils.showModal({
      title: 'Edit List Restrictions',
      content: `
        <div class="form-group">
          <label class="flex items-center gap-2 text-[14px] font-medium text-gray-500">
            <input type="checkbox" id="is-completed-list" class="h-4 w-4 rounded border-gray-300 text-blue-600 focus:ring-blue-500">
            Mark as completion list
          </label>
          <p class="text-sm text-gray-500 mt-2">Completion lists are used to track completed tasks.</p>
        </div>
      `,
      showCancelButton: true,
      confirmButtonText: 'Save',
      cancelButtonText: 'Cancel',
      confirmButtonColor: '#F7B07C',
      cancelButtonColor: '#6c757d',
      focusConfirm: false,
      didOpen: () => {
        // Fetch current status and set checkbox
        fetch(`/admin/crm/lists/${listId}`, {
          headers: {
            'Accept': 'application/json'
          }
        })
        .then(response => response.json())
        .then(data => {
          if (data.is_completed_list) {
            document.getElementById('is-completed-list').checked = true;
          }
        })
        .catch(error => {
          console.error('Error fetching list details:', error);
        });
      },
      preConfirm: () => {
        const isCompletedList = document.getElementById('is-completed-list').checked;
        return { is_completed_list: isCompletedList };
      }
    }).then((result) => {
      if (result.isConfirmed) {
        this.updateListRestrictions(listId, result.value.is_completed_list);
      }
    });
  },
  
  async updateListRestrictions(listId, isCompletedList) {
    try {
      // Show loading state
      modalUtils.showModal({
        title: 'Updating List...',
        text: 'Please wait while we update the list',
        allowOutsideClick: false,
        allowEscapeKey: false,
        showConfirmButton: false,
        willOpen: () => {
          modalUtils.showLoading();
        }
      });
      
      const response = await fetch(`/admin/crm/lists/${listId}`, {
        method: 'PATCH',
        headers: {
          'X-CSRF-Token': document.querySelector('meta[name="csrf-token"]').content,
          'Content-Type': 'application/json',
          'Accept': 'application/json'
        },
        body: JSON.stringify({
          crm_list: {
            is_completed_list: isCompletedList
          }
        })
      });
      
      if (!response.ok) {
        throw new Error(`HTTP error! Status: ${response.status}`);
      }
      
      const data = await response.json();
      
      if (data.success) {
        // Update the list type indicator in the DOM
        const listElement = document.querySelector(`.list-container[data-list-id="${listId}"]`);
        if (listElement) {
          const typeIndicator = listElement.querySelector('span.rounded-full.text-white');
          if (typeIndicator) {
            typeIndicator.textContent = isCompletedList ? 'Completed' : 'Required';
          }
          
          // Update the data attribute
          listElement.dataset.isCompletedList = isCompletedList;
        }
        
        // Show success message
        utils.showSuccess('Success!', 'List restrictions updated successfully');
      } else {
        throw new Error(data.errors || 'Unknown error');
      }
    } catch (error) {
      console.error('Error updating list restrictions:', error);
      utils.showError('Error', error);
    }
  },
  
  confirmDeleteList(listId) {
    utils.confirmDelete(
      'Delete List',
      'Are you sure you want to delete this list? All cards in this list will also be deleted.',
      async () => {
        try {

          
          // Get boardId from the DOM
          const listElement = document.querySelector(`.list-container[data-list-id="${listId}"]`);
          const boardId = listElement ? listElement.getAttribute('data-board-id') : null;
          if (!boardId) {
            throw new Error('Board ID not found for this list');
          }
          const response = await fetch(`/admin/crm/boards/${boardId}/lists/${listId}`, {
            method: 'DELETE',
            headers: {
              'X-CSRF-Token': document.querySelector('meta[name="csrf-token"]').content,
              'Accept': 'application/json'
            }
          });
          
          if (!response.ok) {
            throw new Error(`HTTP error! Status: ${response.status}`);
          }
          
          const data = await response.json();
          
          if (data.success) {
            // Remove the list from the DOM
            const listElement = document.querySelector(`.list-container[data-list-id="${listId}"]`);
            if (listElement) {
              listElement.remove();
            }
            
            // Show success message
            toastr.success('List deleted successfully', 'Success');
          } else {
            throw new Error(data.errors || 'Unknown error');
          }
        } catch (error) {
          console.error('Error deleting list:', error);
          utils.showError('Error', error);
        }
      }
    );
  },
  
  showAddListModal(boardId) {
    if (!boardId) {
      boardId = document.querySelector('[data-board-id]')?.dataset.boardId;
    }
    
    if (!boardId) {
      console.error('No board ID found for adding list');
      return;
    }
    
    // Create custom modal HTML directly
    const modalId = `modal-${Date.now()}`;
    const modalHTML = `
      <div class="fixed inset-0 bg-black/50 z-40 backdrop-blur-sm" id="${modalId}-overlay"></div>
      <div role="dialog" id="${modalId}" aria-labelledby="${modalId}-title" data-state="open" 
           class="fixed left-[50%] top-[50%] z-50 grid w-full max-w-md translate-x-[-50%] translate-y-[-50%] gap-4 duration-200 
                  data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 
                  data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 
                  data-[state=closed]:slide-out-to-left-1/2 data-[state=closed]:slide-out-to-top-[48%] 
                  data-[state=open]:slide-in-from-left-1/2 data-[state=open]:slide-in-from-top-[48%] 
                  sm:rounded-lg rounded-2xl border-0 shadow-[0_20px_60px_-15px_rgba(0,0,0,0.15)] 
                  p-0 overflow-hidden bg-white/95 backdrop-blur-md" 
           tabindex="-1" style="pointer-events: auto;">
        <div class="bg-gradient-to-b from-white to-gray-50/50 p-8 backdrop-blur-sm">
          <div class="flex flex-col space-y-1.5 text-center sm:text-left pb-5">
            <h2 id="${modalId}-title" class="tracking-tight text-xl font-medium text-center text-gray-800">Add New List</h2>
          </div>
          
          <div class="space-y-2 mb-4">
            <label for="list-title" class="text-[14px] font-medium text-gray-500">List Title</label>
            <input id="list-title" type="text" 
                   class="flex w-full border py-2 ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium file:text-foreground placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 rounded-xl border-gray-200/80 h-11 px-4 focus-visible:ring-gray-300 text-[14px] bg-gray-50/30 backdrop-blur-sm" 
                   placeholder="Enter list title" required>
            <p class="input-error text-red-500 text-sm mt-1 hidden"></p>
          </div>
          
          <div class="flex gap-3 pt-4">
            <button type="button" class="cancel-btn inline-flex items-center justify-center gap-2 whitespace-nowrap 
                    ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring 
                    focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none 
                    [&_svg]:size-4 [&_svg]:shrink-0 border bg-background px-4 py-2 flex-1 rounded-xl h-10 
                    border-gray-100 text-gray-500 hover:bg-gray-50/70 hover:text-gray-600 transition-colors text-[14px] font-medium">Cancel</button>
            
            <button type="button" class="confirm-btn inline-flex items-center justify-center gap-2 whitespace-nowrap 
                    ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring 
                    focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none 
                    [&_svg]:size-4 [&_svg]:shrink-0 px-4 py-2 flex-1 rounded-xl h-10 
                    bg-sky-300 hover:bg-sky-400 text-white transition-colors text-[14px] font-medium shadow-sm">Create List</button>
          </div>
        </div>
        
        <button type="button" class="close-btn absolute right-4 top-4 rounded-sm opacity-70 ring-offset-background 
                transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-ring 
                focus:ring-offset-2 disabled:pointer-events-none data-[state=open]:bg-accent data-[state=open]:text-muted-foreground">
          <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" 
               stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" 
               class="lucide lucide-x h-4 w-4">
            <path d="M18 6 6 18"></path>
            <path d="m6 6 12 12"></path>
          </svg>
          <span class="sr-only">Close</span>
        </button>
      </div>
    `;
    
    // Add modal to container
    document.getElementById('custom-modal-container').innerHTML = modalHTML;
    
    // Get modal elements
    const modal = document.getElementById(modalId);
    const overlay = document.getElementById(`${modalId}-overlay`);
    const confirmButton = modal.querySelector('.confirm-btn');
    const cancelButton = modal.querySelector('.cancel-btn');
    const closeButton = modal.querySelector('.close-btn');
    const inputElement = document.getElementById('list-title');
    const errorElement = modal.querySelector('.input-error');
    
    // Close modal function
    const closeModal = () => {
      modal.setAttribute('data-state', 'closed');
      
      // Remove modal and overlay after animation completes
      setTimeout(() => {
        if (modal.parentNode) {
          modal.parentNode.removeChild(modal);
        }
        if (overlay.parentNode) {
          overlay.parentNode.removeChild(overlay);
        }
      }, 300);
    };
    
    // Handle confirm button click
    confirmButton.addEventListener('click', () => {
      const title = inputElement.value;
      
      if (!title) {
        errorElement.textContent = 'Please enter a list title';
        errorElement.classList.remove('hidden');
        return;
      }
      
      // Close the modal
      closeModal();
      
      // Create the list
      this.createList(boardId, title);
    });
    
    // Handle cancel button click
    cancelButton.addEventListener('click', closeModal);
    
    // Handle close button click
    closeButton.addEventListener('click', closeModal);
    
    // Handle overlay click
    overlay.addEventListener('click', closeModal);
  },
  
  async createList(boardId, title) {
    try {
      // Show loading state
      modalUtils.showModal({
        title: 'Creating List...',
        text: 'Please wait while we create the list',
        allowOutsideClick: false,
        allowEscapeKey: false,
        showConfirmButton: false,
        willOpen: () => {
          modalUtils.showLoading();
        }
      });
      
      const response = await fetch(`/admin/crm/boards/${boardId}/lists`, {
        method: 'POST',
        headers: {
          'X-CSRF-Token': document.querySelector('meta[name="csrf-token"]').content,
          'Content-Type': 'application/json',
          'Accept': 'application/json'
        },
        body: JSON.stringify({
          crm_list: {
            title
          }
        })
      });
      
      if (!response.ok) {
        throw new Error(`HTTP error! Status: ${response.status}`);
      }
      
      const data = await response.json();
      
      if (data.success) {
        // Show success message using toastr
        toastr.success('List created successfully', 'Success');
        
        // Refresh the page to show the new list
        setTimeout(() => {
          window.location.reload();
        }, 1500);
        
        // The code below will not execute due to the page reload
        return;
      } else {
        throw new Error(data.errors || 'Unknown error');
      }
    } catch (error) {
      toastr.error(error.message || 'Failed to create list', 'Error');
    }
  },
  
  initAddCardButton(listElement) {
    const addButton = listElement.querySelector('.toggle-add-card-btn');
    if (addButton) {
      addButton.addEventListener('click', (e) => {
        e.preventDefault();
        const listId = listElement.dataset.listId;
        if (listId) {
          patientController.showSearchModal(listId);
        } 
      });
    }
  },
  
  initCancelAddCard(listElement) {
    // Implementation for cancel add card button if needed
  },
  
  initEditForm(listElement) {
    const listTitle = listElement.querySelector('.list-title');
    const listTitleText = listElement.querySelector('.list-title-text');
    const listTitleInput = listElement.querySelector('.list-title-input');
    
    if (!listTitle || !listTitleText || !listTitleInput) return;
    
    // Enable editing on click
    listTitleText.addEventListener('click', () => {
      // Hide the text and show the input
      listTitleText.classList.add('hidden');
      listTitleInput.classList.remove('hidden');
      
      // Focus the input and select all text
      listTitleInput.focus();
      listTitleInput.select();
    });
    
    // Handle saving on enter key press
    listTitleInput.addEventListener('keydown', (e) => {
      if (e.key === 'Enter') {
        e.preventDefault();
        saveListTitle();
      } else if (e.key === 'Escape') {
        e.preventDefault();
        cancelEditing();
      }
    });
    
    // Handle saving when input loses focus
    listTitleInput.addEventListener('blur', () => {
      saveListTitle();
    });
    
    // Function to save the list title
    const saveListTitle = () => {
      const newTitle = listTitleInput.value.trim();
      const listId = listTitle.dataset.listId;
      const originalTitle = listTitle.dataset.originalTitle;
      
      // Only update if the title has changed
      if (newTitle && newTitle !== originalTitle) {
        // Update the UI immediately
        const truncatedTitle = newTitle.length > 20 ? newTitle.substring(0, 20) + '...' : newTitle;
        listTitleText.textContent = truncatedTitle;

        // Update data attribute and tooltip
        listTitle.dataset.originalTitle = newTitle;
        if (newTitle.length > 20) {
          listTitleText.setAttribute('title', newTitle);
        } else {
          listTitleText.removeAttribute('title');
        }
        
        // Save to the server
        this.updateListTitle(listId, newTitle);
      }
      
      // Hide input and show text
      listTitleText.classList.remove('hidden');
      listTitleInput.classList.add('hidden');
    };
    
    // Function to cancel editing
    const cancelEditing = () => {
      // Reset the input value
      listTitleInput.value = listTitle.dataset.originalTitle;
      
      // Hide input and show text
      listTitleText.classList.remove('hidden');
      listTitleInput.classList.add('hidden');
    };
  },
  
  initEditRestrictions(listElement) {
    // Implementation for edit restrictions if needed
  },
  
  initDeleteButton(listElement) {
    // Implementation for delete button if needed
  },
  
  showTableView(listId, listElement) {
    const listTitleElement = listElement.querySelector('h3');
    const listTitle = listTitleElement.dataset.originalTitle || listTitleElement.textContent.trim();
    
    // Get all cards in the list
    const cardElements = listElement.querySelectorAll('[data-card-id]');
    
    // Create table HTML
    let tableHtml = `
      <div class="overflow-x-auto">
        <table class="min-w-full bg-white border border-gray-200">
          <thead>
            <tr class="bg-gray-100">
              <th class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Patient</th>
              <th class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
              <th class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Created</th>
              <th class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
            </tr>
          </thead>
          <tbody>
    `;
    
    // If no cards, show empty message
    if (cardElements.length === 0) {
      tableHtml += `
        <tr>
          <td colspan="4" class="px-4 py-4 text-center text-sm text-gray-500">No patients in this list</td>
        </tr>
      `;
    } else {
      // Add rows for each card
      cardElements.forEach(card => {
        const patientName = card.querySelector('.font-medium')?.textContent.trim() || 'Unknown';
        const status = card.querySelector('.text-\\[11px\\].px-2.py-0\\.5.rounded-full')?.textContent.trim() || 'N/A';
        
        tableHtml += `
          <tr class="border-t border-gray-200">
            <td class="px-4 py-3 text-sm">${patientName}</td>
            <td class="px-4 py-3 text-sm">
              <span class="px-2 py-1 text-xs rounded-full ${status.toLowerCase().includes('completed') ? 'bg-green-100 text-green-800' : 'bg-blue-100 text-blue-800'}">
                ${status}
              </span>
            </td>
            <td class="px-4 py-3 text-sm">Today</td>
            <td class="px-4 py-3 text-sm">
              <button class="text-blue-500 hover:text-blue-700 mr-2">View</button>
              <button class="text-red-500 hover:text-red-700">Delete</button>
            </td>
          </tr>
        `;
      });
    }
    
    tableHtml += `
          </tbody>
        </table>
      </div>
    `;
    
    // Show the table in a modal
    modalUtils.showModal({
      title: `${listTitle} - Table View`,
      content: tableHtml,
      width: '800px',
      showCloseButton: true,
      showConfirmButton: false,
      customClass: {
        width: 'max-w-3xl',
        popup: 'rounded-lg',
        header: 'border-b border-gray-200 pb-3'
      }
    });
  }
};

export default listController;
