// Patient Notes Modal functionality
// Uses modalUtils from CRM for a consistent UI
// Both new and update note modals are generated dynamically, no static modal IDs are used.

document.addEventListener('DOMContentLoaded', () => {
  // Initialize the new patient note button
  const newNoteBtn = document.getElementById('new-patient-note-btn');
  if (newNoteBtn) {
    newNoteBtn.addEventListener('click', openNewNoteModal);
  }

  // Note: Event listeners for note menus and color pickers are handled by
  // note_menu.js and note_color_picker.js using event delegation
});

/**
 * Opens the new patient note modal
 */
function openNewNoteModal() {
  // Get the patient ID from the page
  const patientId = document.querySelector('meta[name="patient-id"]')?.content;
  
  if (!patientId) {
    return;
  }

  // Create the modal content with a form
  const content = `
    <form id="new-patient-note-form" action="/admin/patient_notes" method="post" class="space-y-4">
      <input type="hidden" name="authenticity_token" value="${Rails.csrfToken()}">
      <input type="hidden" name="patient_note[patient_id]" value="${patientId}">
      
      <div class="space-y-2">
        <label for="patient_note_title" class="block text-sm font-medium text-gray-700">Title</label>
        <div class="relative">
          <input type="text" name="patient_note[title]" id="patient_note_title" required
                 class="w-full rounded-md border border-gray-300 px-3 py-2 text-gray-900 placeholder-gray-400 focus:border-blue-500 focus:outline-none focus:ring-1 focus:ring-blue-500 sm:text-sm">
          <button type="button" id="note_ai_title" class="absolute right-2 bottom-2 flex h-6 w-6 items-center justify-center rounded-full border border-gray-200 bg-white text-gray-500 hover:bg-gray-50">
            <svg width="16" height="16" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path d="M17.5 9.16667V7.5H15.8333V5.83333C15.832 5.39171 15.656 4.96856 15.3438 4.65628C15.0314 4.344 14.6082 4.16798 14.1667 4.16667H12.5V2.5H10.8333V4.16667H9.16667V2.5H7.5V4.16667H5.83333C5.39171 4.16798 4.96856 4.344 4.65628 4.65628C4.344 4.96856 4.16798 5.39171 4.16667 5.83333V7.5H2.5V9.16667H4.16667V10.8333H2.5V12.5H4.16667V14.1667C4.16798 14.6082 4.344 15.0314 4.65628 15.3438C4.96856 15.656 5.39171 15.832 5.83333 15.8333H7.5V17.5H9.16667V15.8333H10.8333V17.5H12.5V15.8333H14.1667C14.6082 15.832 15.0314 15.656 15.3438 15.3438C15.656 15.0314 15.832 14.6082 15.8333 14.1667V12.5H17.5V10.8333H15.8333V9.16667H17.5ZM14.1667 14.1667H5.83333V5.83333H14.1667V14.1667Z" fill="#3F3F3F"></path>
              <path d="M9.46625 6.66667H8.34542L6.67043 13.3333H7.52627L7.91293 11.7708H9.84292L10.2196 13.3333H11.1046L9.46625 6.66667ZM8.02543 11.1033L8.87375 7.45833H8.91208L9.73125 11.1033H8.02543ZM11.8688 6.66667H12.7021V13.3333H11.8688V6.66667Z" fill="#6D6B6B"></path>
            </svg>
          </button>
        </div>
      </div>
      
      <div class="space-y-2">
        <label for="patient_note_text" class="block text-sm font-medium text-gray-700">Note</label>
        <textarea name="patient_note[text]" id="patient_note_text" rows="4"
                  class="w-full rounded-md border border-gray-300 px-3 py-2 text-gray-900 placeholder-gray-400 focus:border-blue-500 focus:outline-none focus:ring-1 focus:ring-blue-500 sm:text-sm"></textarea>
      </div>
      
      <div class="space-y-2">
        <label for="patient_note_color" class="block text-sm font-medium text-gray-700">Color</label>
        <div class="flex space-x-3">
          <button type="button" class="note-color-option h-6 w-6 rounded-full bg-amber-200 hover:ring-2 hover:ring-amber-400 hover:ring-offset-2" data-color="yellow"></button>
          <button type="button" class="note-color-option h-6 w-6 rounded-full bg-blue-200 hover:ring-2 hover:ring-blue-400 hover:ring-offset-2" data-color="blue"></button>
          <button type="button" class="note-color-option h-6 w-6 rounded-full bg-green-200 hover:ring-2 hover:ring-green-400 hover:ring-offset-2" data-color="green"></button>
          <button type="button" class="note-color-option h-6 w-6 rounded-full bg-pink-200 hover:ring-2 hover:ring-pink-400 hover:ring-offset-2" data-color="pink"></button>
        </div>
        <input type="hidden" name="patient_note[color]" id="patient_note_color" value="green">
      </div>
    </form>
  `;

  // Show the modal
  modalUtils.showModal({
    title: 'New Patient Note',
    content: content,
    html: true,
    width: 'max-w-md',
    showConfirmButton: true,
    confirmButtonText: 'Create Note',
    showCancelButton: true,
    cancelButtonText: 'Cancel',
    confirmButtonColor: 'bg-blue-500 hover:bg-blue-600',
    onConfirm: () => {
      // Submit the form
      const form = document.getElementById('new-patient-note-form');
      if (form) {
        // Validate the form
        if (!form.checkValidity()) {
          form.reportValidity();
          return false;
        }

        // Submit the form via AJAX
        const formData = new FormData(form);
        
        // Ensure color is set (default to green if missing)
        if (!formData.get('patient_note[color]')) {
          formData.set('patient_note[color]', 'green');
        }
        
        fetch(form.action, {
          method: 'POST',
          headers: {
            'X-CSRF-Token': Rails.csrfToken(),
            'Accept': 'application/json'
          },
          body: formData
        })
        .then(response => {
          if (response.ok) {
            return response.json();
          }
          throw new Error('Network response was not ok');
        })
        .then(data => {
          
          // Show success message with toastr
          toastr.success('Note created successfully');
          
          // Use the data directly from the server response
          const noteData = {
            id: data.id,
            title: data.title,
            text: data.text,
            color: data.color,
            user_name: data.user_name,
            created_at: data.created_at
          };
          
          // Add the new note to the DOM
          addNoteToDOM(noteData);
          
          // Close the modal
          modalUtils.closeModal();
        })
        .catch(error => {
          console.error('Error:', error);
          toastr.error('Failed to create note');
        });
      }
    }
  });

  // Add event listeners for color selection
  setTimeout(() => {
    const colorOptions = document.querySelectorAll('.note-color-option');
    colorOptions.forEach(option => {
      option.addEventListener('click', function() {
        // Remove active class from all options
        colorOptions.forEach(opt => opt.classList.remove('ring-2', 'ring-offset-2'));
        
        // Add active class to selected option
        this.classList.add('ring-2', 'ring-offset-2');
        
        // Set the color value
        document.getElementById('patient_note_color').value = this.dataset.color;
      });
    });

    // Set default color
    const defaultColor = document.querySelector('.note-color-option[data-color="green"]');
    if (defaultColor) {
      defaultColor.classList.add('ring-2', 'ring-offset-2');
      document.getElementById('patient_note_color').value = 'green';
    }

    // Attach event listener to AI title button
    const aiButton = document.getElementById('note_ai_title');
    if (aiButton && typeof window.handleAiGenerateTitleFromNote === 'function') {
      aiButton.addEventListener('mousedown', window.handleAiGenerateTitleFromNote);
    }
  }, 100);
}

/**
 * Adds a new note to the DOM
 * @param {Object} noteData - The note data from the server
 */
function addNoteToDOM(noteData) {
  
  // Look for the notes container by its specific ID
  const notesContainer = document.getElementById('patient-notes-container');
  if (!notesContainer) {
    console.error('ERROR: Notes container not found');
    return;
  }
  
  // Initialize note content structure
  // The data might be nested under different properties
  let noteContent = noteData;
  
  // Check for nested structures
  if (noteData.note) {
    noteContent = noteData.note;
  } else if (noteData.patient_note) {
    noteContent = noteData.patient_note;
  }
  
  
  
  // Format the date with validation
  let formattedDate = '';
  
  if (noteData.created_at) {
    try {
      // Try to handle different date formats
      let date;
      
      // Check if it's already in DD/MM/YYYY format
      if (typeof noteData.created_at === 'string' && noteData.created_at.includes('/')) {
        const parts = noteData.created_at.split(' ')[0].split('/');
        if (parts.length === 3) {
          // Convert from DD/MM/YYYY to YYYY-MM-DD for proper parsing
          const day = parts[0].padStart(2, '0');
          const month = parts[1].padStart(2, '0');
          const year = parts[2];
          date = new Date(`${year}-${month}-${day}T${noteData.created_at.split(' ')[1] || '00:00'}`);
        } else {
          date = new Date(noteData.created_at);
        }
      } else {
        date = new Date(noteData.created_at);
      }
      
      
      
      if (!isNaN(date.getTime())) { // Check if date is valid
        formattedDate = date.toLocaleDateString('en-GB', {
          day: 'numeric',
          month: 'numeric',
          year: 'numeric'
        }) + ' ' + date.toLocaleTimeString('en-GB', { 
          hour: '2-digit', 
          minute: '2-digit' 
        });
      } else {
        formattedDate = 'Just now';
      }
    } catch (e) {
      formattedDate = 'Just now';
    }
  } else {
    formattedDate = 'Just now';
  }
  
  // Map the color name to match the view expectations
  // The API might return different color formats
  const colorMap = {
    'yellow': 'amber',
    'amber': 'amber',
    'blue': 'blue',
    'green': 'green',
    'pink': 'pink'
  };
  
  // Get the color directly from noteData (which already has the form value)
  let originalColor = noteData.color || 'green';
  originalColor = originalColor.trim().toLowerCase();
  
  
  
  // Default to green if no color is found
  let color = 'green';
  
  // Map the color if it exists in our map
  if (originalColor && colorMap[originalColor]) {
    color = colorMap[originalColor];
  }
  
  
  
  // Force color to be green for debugging
  // color = 'green';
  // console.log('DEBUG: Forcing color to green for testing');
  
  // Extract user name from the potentially nested structure
  let userName = 'Admin User'; // Default fallback
  
  // First check if the user name is in the note data
  if (noteData.user_name) {
    userName = noteData.user_name;
  } else if (noteContent.user_name) {
    userName = noteContent.user_name;
  } else if (noteData.note && noteData.note.user_name) {
    userName = noteData.note.user_name;
  } else if (noteData.created_by_name) {
    userName = noteData.created_by_name;
  } else if (noteContent.created_by_name) {
    userName = noteContent.created_by_name;
  }
  
  // If we still don't have a user name, try to get it from the global variable
  if (userName === 'Admin User') {
    if (window._currentUserName) {
      userName = window._currentUserName;
    } else if (document.getElementById('current-user-name')) {
      userName = document.getElementById('current-user-name').textContent.trim();
    }
  }
  
  
  // Extract all needed fields with fallbacks
  let noteId = 'new';
  if (noteContent.id) {
    noteId = noteContent.id;
  } else if (noteData.id) {
    noteId = noteData.id;
  }
  
  
  // Use the title from noteData (which now contains the form values)
  let noteTitle = noteData.title || 'Note';
  
  
  // Use the text from noteData (which now contains the form values)
  let noteText = noteData.text || '';
  
  
  
  // Create the note HTML
  const noteHTML = `
    <div class="bg-${color}-100 rounded-lg p-4 relative border-0 shadow-lg hover:scale-[1.01] transition-transform duration-200" data-note-id="${noteId}" style="box-shadow: rgba(${color === 'amber' ? '251, 191, 36' : color === 'blue' ? '59, 130, 246' : color === 'green' ? '34, 197, 94' : '244, 114, 182'}, 0.1) 0px 10px 15px -3px, rgba(${color === 'amber' ? '251, 191, 36' : color === 'blue' ? '59, 130, 246' : color === 'green' ? '34, 197, 94' : '244, 114, 182'}, 0.2) 0px 4px 6px -4px;">
      <div class="absolute right-0 top-0 w-8 h-8 bg-${color}-200/80 rounded-bl-xl" style="clip-path: polygon(100% 0px, 0px 0px, 100% 100%);"></div>
      <div class="flex justify-between items-start mb-2">
        <span class="text-[11px] px-2 py-0.5 bg-${color}-200/80 rounded-full text-${color}-800 font-medium">${userName}</span>
        <div class="flex gap-1">
          <div class="relative">
            <button class="note-menu-button inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 [&amp;_svg]:pointer-events-none [&amp;_svg]:size-4 [&amp;_svg]:shrink-0 h-6 w-6 text-${color}-600 hover:text-${color}-800 hover:bg-${color}-200/50" data-note-id="${noteId}">
              <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-ellipsis h-3.5 w-3.5">
                <circle cx="12" cy="12" r="1"></circle>
                <circle cx="19" cy="12" r="1"></circle>
                <circle cx="5" cy="12" r="1"></circle>
              </svg>
            </button>
            <div class="note-menu absolute right-0 z-50 hidden w-36 mt-1 py-1 bg-white rounded-md shadow-lg" data-note-id="${noteId}">
              <button type="button" class="flex items-center px-3 py-1.5 text-[13px] text-gray-700 hover:bg-gray-100 w-full text-left pin-note-btn" data-note-id="${noteId}">
                Pin note
              </button>
              <button type="button" class="flex items-center px-3 py-1.5 text-[13px] text-gray-700 hover:bg-gray-100 w-full text-left update-note-btn" data-note-id="${noteId}" onclick="openNoteModal(${noteId}, true)">
                Update note
              </button>
              <button type="button" class="flex items-center px-3 py-1.5 text-[13px] text-red-600 hover:bg-gray-100 w-full text-left archive-note-btn" data-note-id="${noteId}">
                Archive note
              </button>
            </div>
          </div>
          <div class="relative">
            <button class="note-color-picker-btn inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 [&amp;_svg]:pointer-events-none [&amp;_svg]:size-4 [&amp;_svg]:shrink-0 h-6 w-6 text-${color}-600 hover:text-${color}-800 hover:bg-${color}-200/50" data-note-id="${noteId}">
              <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-droplet h-3.5 w-3.5">
                <path d="M12 2.69l5.66 5.66a8 8 0 1 1-11.31 0z"></path>
              </svg>
            </button>
            <div class="note-color-picker absolute right-0 top-full mt-1 p-2 bg-white rounded-md shadow-lg z-10 hidden flex-wrap gap-2" style="width: 100px;">
              <button type="button" class="note-color-option h-6 w-6 rounded-full bg-yellow-200 hover:ring-2 hover:ring-yellow-400 hover:ring-offset-2" data-color="yellow" data-note-id="${noteId}"></button>
              <button type="button" class="note-color-option h-6 w-6 rounded-full bg-blue-200 hover:ring-2 hover:ring-blue-400 hover:ring-offset-2" data-color="blue" data-note-id="${noteId}"></button>
              <button type="button" class="note-color-option h-6 w-6 rounded-full bg-green-200 hover:ring-2 hover:ring-green-400 hover:ring-offset-2" data-color="green" data-note-id="${noteId}"></button>
              <button type="button" class="note-color-option h-6 w-6 rounded-full bg-pink-200 hover:ring-2 hover:ring-pink-400 hover:ring-offset-2" data-color="pink" data-note-id="${noteId}"></button>
            </div>
          </div>
        </div>
      </div>
      <h4 class="font-medium text-[14px] text-${color}-900">${noteTitle}</h4>
      <p class="text-[11px] text-${color}-700/70 mb-2 note-date">${formattedDate}</p>
      <p class="text-[13px] text-${color}-800">${noteText}</p>
    </div>
  `;
  
  // Create a temporary container
  const tempContainer = document.createElement('div');
  tempContainer.innerHTML = noteHTML;
  const noteElement = tempContainer.firstElementChild;
  
  // Insert the note at the beginning of the container
  if (notesContainer.firstChild) {
    notesContainer.insertBefore(noteElement, notesContainer.firstChild);
  } else {
    // Fallback: append to the container
    notesContainer.appendChild(noteElement);
  }

  // Event listeners are handled automatically by event delegation in note_menu.js and note_color_picker.js
}

// Export the function for use in other files
export { openNewNoteModal };

/**
 * Opens the update patient note modal using modalUtils
 * @param {string|number} noteId
 */
function openUpdateNoteModal(noteId) {
  if (!noteId) {
    return;
  }
  // Fetch note data
  fetch(`/admin/patient_notes/${noteId}/edit`, {
    headers: {
      'Accept': 'application/json',
      'X-Requested-With': 'XMLHttpRequest'
    }
  })
  .then(response => response.json())
  .then(data => {
    // Build the modal content with pre-filled form
    const patientId = document.querySelector('meta[name="patient-id"]')?.content;
    const content = `
      <form id="update-patient-note-form" action="/admin/patient_notes/${noteId}" method="post" class="space-y-4">
        <input type="hidden" name="_method" value="patch">
        <input type="hidden" name="authenticity_token" value="${Rails.csrfToken()}">
        <input type="hidden" name="patient_note[patient_id]" value="${patientId}">
        <input type="hidden" name="patient_note[patient_note_id]" value="${noteId}">
        <div class="space-y-2">
          <label for="patient_note_title" class="block text-sm font-medium text-gray-700">Title</label>
          <div class="relative">
            <input type="text" name="patient_note[title]" id="patient_note_title" required
                   class="w-full rounded-md border border-gray-300 px-3 py-2 text-gray-900 placeholder-gray-400 focus:border-blue-500 focus:outline-none focus:ring-1 focus:ring-blue-500 sm:text-sm"
                   value="${data.title || ''}">
          </div>
        </div>
        <div class="space-y-2">
          <label for="patient_note_text" class="block text-sm font-medium text-gray-700">Note</label>
          <textarea name="patient_note[text]" id="patient_note_text" rows="4"
                    class="w-full rounded-md border border-gray-300 px-3 py-2 text-gray-900 placeholder-gray-400 focus:border-blue-500 focus:outline-none focus:ring-1 focus:ring-blue-500 sm:text-sm">${data.text || ''}</textarea>
        </div>
        <div class="space-y-2">
          <label for="patient_note_color" class="block text-sm font-medium text-gray-700">Color</label>
          <div class="flex space-x-3">
            <button type="button" class="note-color-option h-6 w-6 rounded-full bg-amber-200 hover:ring-2 hover:ring-amber-400 hover:ring-offset-2" data-color="yellow"></button>
            <button type="button" class="note-color-option h-6 w-6 rounded-full bg-blue-200 hover:ring-2 hover:ring-blue-400 hover:ring-offset-2" data-color="blue"></button>
            <button type="button" class="note-color-option h-6 w-6 rounded-full bg-green-200 hover:ring-2 hover:ring-green-400 hover:ring-offset-2" data-color="green"></button>
            <button type="button" class="note-color-option h-6 w-6 rounded-full bg-pink-200 hover:ring-2 hover:ring-pink-400 hover:ring-offset-2" data-color="pink"></button>
          </div>
          <input type="hidden" name="patient_note[color]" id="patient_note_color" value="${data.color || 'yellow'}">
        </div>
      </form>
    `;
    // Show the modal
    modalUtils.showModal({
      title: 'Update Note',
      content: content,
      html: true,
      width: 'max-w-md',
      showConfirmButton: true,
      confirmButtonText: 'Update Note',
      showCancelButton: true,
      cancelButtonText: 'Cancel',
      confirmButtonColor: 'bg-blue-500 hover:bg-blue-600',
      onConfirm: () => {
        const form = document.getElementById('update-patient-note-form');
        if (form) {
          if (!form.checkValidity()) {
            form.reportValidity();
            return false;
          }
          const formData = new FormData(form);
          fetch(form.action, {
            method: 'POST',
            headers: {
              'X-CSRF-Token': Rails.csrfToken(),
              'Accept': 'application/json'
            },
            body: formData
          })
          .then(response => {
            if (response.ok) {
              return response.json();
            }
            throw new Error('Network response was not ok');
          })
          .then(data => {
            // Use toastr for success message
            toastr.success('Note updated successfully');
            
            // Update the note in the DOM
            updateNoteInDOM(data);
          })
          .catch(error => {
            // Use toastr for error message
            toastr.error('Failed to update note');
          });
        }
      }
    });
    // Add event listeners for color selection
    setTimeout(() => {
      const colorOptions = document.querySelectorAll('.note-color-option');
      colorOptions.forEach(option => {
        option.addEventListener('click', function() {
          colorOptions.forEach(opt => opt.classList.remove('ring-2', 'ring-offset-2'));
          this.classList.add('ring-2', 'ring-offset-2');
          document.getElementById('patient_note_color').value = this.dataset.color;
        });
      });
      // Set selected color
      const selectedColor = document.querySelector(`.note-color-option[data-color="${data.color || 'yellow'}"`);
      if (selectedColor) {
        selectedColor.classList.add('ring-2', 'ring-offset-2');
      }
    }, 100);
  })
  .catch(error => {
    // Use toastr instead of modal for error message
    toastr.error('Failed to load note data');
  });
}

/**
 * Updates an existing note in the DOM
 * @param {Object} noteData - The updated note data from the server
 */
function updateNoteInDOM(noteData) {
  // Find the note element
  const noteElement = document.querySelector(`[data-note-id="${noteData.id}"]`);
  if (!noteElement) {
    return;
  }
  
  // Format the date
  const date = new Date(noteData.created_at);
  const formattedDate = date.toLocaleDateString('en-GB') + ' ' + 
                      date.toLocaleTimeString('en-GB', { hour: '2-digit', minute: '2-digit' });
  
  // Map the color name to match the view expectations
  const colorMap = {
    'yellow': 'amber',
    'blue': 'blue',
    'green': 'green',
    'pink': 'pink'
  };
  
  const oldColor = noteElement.className.match(/bg-(\w+)-100/)?.[1] || 'amber';
  const newColor = colorMap[noteData.color] || 'amber';
  
  // Update the note's background color class
  noteElement.className = noteElement.className.replace(new RegExp(`bg-${oldColor}-100`), `bg-${newColor}-100`);
  
  // Update the box shadow
  const shadowColor = newColor === 'amber' ? '251, 191, 36' : 
                      newColor === 'blue' ? '59, 130, 246' : 
                      newColor === 'green' ? '34, 197, 94' : 
                      '244, 114, 182';
  
  noteElement.style.boxShadow = `rgba(${shadowColor}, 0.1) 0px 10px 15px -3px, rgba(${shadowColor}, 0.2) 0px 4px 6px -4px`;
  
  // Update the corner accent
  const cornerAccent = noteElement.querySelector(`[class*="bg-${oldColor}-200"]`);
  if (cornerAccent) {
    cornerAccent.className = cornerAccent.className.replace(new RegExp(`bg-${oldColor}-200`), `bg-${newColor}-200`);
  }
  
  // Update the user label
  const userLabel = noteElement.querySelector(`[class*="bg-${oldColor}-200/80"]`);
  if (userLabel) {
    userLabel.className = userLabel.className
      .replace(new RegExp(`bg-${oldColor}-200/80`), `bg-${newColor}-200/80`)
      .replace(new RegExp(`text-${oldColor}-800`), `text-${newColor}-800`);
  }
  
  // Update the menu buttons
  noteElement.querySelectorAll(`[class*="text-${oldColor}-600"]`).forEach(el => {
    el.className = el.className
      .replace(new RegExp(`text-${oldColor}-600`), `text-${newColor}-600`)
      .replace(new RegExp(`hover:text-${oldColor}-800`), `hover:text-${newColor}-800`)
      .replace(new RegExp(`hover:bg-${oldColor}-200/50`), `hover:bg-${newColor}-200/50`);
  });
  
  // Update the title
  const title = noteElement.querySelector(`[class*="text-${oldColor}-900"]`);
  if (title) {
    title.className = title.className.replace(new RegExp(`text-${oldColor}-900`), `text-${newColor}-900`);
    title.textContent = noteData.title;
  }
  
  // Update the date
  const dateElement = noteElement.querySelector(`[class*="text-${oldColor}-700/70"]`);
  if (dateElement) {
    dateElement.className = dateElement.className.replace(new RegExp(`text-${oldColor}-700/70`), `text-${newColor}-700/70`);
    // Don't update the date text as it's the creation date, not the update date
  }
  
  // Update the text content
  const textElement = noteElement.querySelector(`[class*="text-${oldColor}-800"]:not([class*="font-medium"])`);
  if (textElement) {
    textElement.className = textElement.className.replace(new RegExp(`text-${oldColor}-800`), `text-${newColor}-800`);
    textElement.textContent = noteData.text;
  }
}

/**
 * Initializes event listeners for a note element
 * @param {HTMLElement} noteElement - The note element to initialize
 */
function initializeNoteEventListeners(noteElement) {
  if (!noteElement) return;
  
  // Menu button click handler
  const menuButton = noteElement.querySelector('.note-menu-button');
  const menu = noteElement.querySelector('.note-menu');
  
  if (menuButton && menu) {
    menuButton.addEventListener('click', function(e) {
      e.stopPropagation();
      menu.classList.toggle('hidden');
      
      // Close other open menus
      document.querySelectorAll('.note-menu:not(.hidden)').forEach(openMenu => {
        if (openMenu !== menu) {
          openMenu.classList.add('hidden');
        }
      });
    });
  }
  
  // Color picker button click handler
  const colorPickerBtn = noteElement.querySelector('.note-color-picker-btn');
  const colorPicker = noteElement.querySelector('.note-color-picker');
  
  if (colorPickerBtn && colorPicker) {
    colorPickerBtn.addEventListener('click', function(e) {
      e.stopPropagation();
      colorPicker.classList.toggle('hidden');
      colorPicker.classList.toggle('flex');
      
      // Close other open color pickers
      document.querySelectorAll('.note-color-picker:not(.hidden)').forEach(openPicker => {
        if (openPicker !== colorPicker) {
          openPicker.classList.add('hidden');
          openPicker.classList.remove('flex');
        }
      });
    });
    
    // Color option click handlers are handled by note_color_picker.js
    // We don't need to attach event listeners here to avoid duplicate API calls
  }
  
  // Close menus when clicking outside
  document.addEventListener('click', function() {
    document.querySelectorAll('.note-menu:not(.hidden)').forEach(menu => {
      menu.classList.add('hidden');
    });
    
    document.querySelectorAll('.note-color-picker:not(.hidden)').forEach(picker => {
      picker.classList.add('hidden');
      picker.classList.remove('flex');
    });
  });
}
