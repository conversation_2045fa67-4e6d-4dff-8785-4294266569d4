// Patient Notes JavaScript
// <PERSON>les creating and updating patient notes

// Function to open the note modal for creating or updating
window.openNoteModal = function(noteId = null) {
  console.log('Opening note modal with ID:', noteId);
  
  // If we're creating a new note (no noteId), show toast notification instead of modal
  if (!noteId) {
    // Show toast notification
    toastr.info('Creating a new note...', '', {
      timeOut: 1000,
      positionClass: 'toast-top-right'
    });
    
    // Create a default note via AJAX
    const patientId = document.querySelector('input[name="patient_note[patient_id]"]')?.value;
    if (!patientId) {
      toastr.error('Patient ID not found');
      return;
    }
    
    // Create form data for the new note
    const formData = new FormData();
    formData.append('patient_note[patient_id]', patientId);
    formData.append('patient_note[title]', 'New Note');
    formData.append('patient_note[text]', '');
    formData.append('patient_note[color]', 'blue');
    
    // Send the request
    fetch('/admin/patient_notes', {
      method: 'POST',
      body: formData,
      headers: {
        'X-Requested-With': 'XMLHttpRequest',
        'X-CSRF-Token': document.querySelector('meta[name="csrf-token"]')?.content
      }
    })
    .then(response => response.json())
    .then(data => {
      console.log('Note created successfully:', data);
      toastr.success('Note created successfully');
      
      // Reload the page to show the new note
      setTimeout(() => {
        window.location.reload();
      }, 500);
    })
    .catch(error => {
      console.error('Error creating note:', error);
      toastr.error('Failed to create note');
    });
    
    return;
  }
  
  // For updating existing notes, continue with the modal
  const modal = document.getElementById('patient-note-modal');
  if (!modal) {
    console.error('Modal not found in DOM');
    return;
  }
  
  // Reset form
  document.getElementById('patient-note-form').reset();
  
  // Default color selection
  selectNoteColor(document.querySelector('.note-color-option[data-color="blue"]'), 'blue');
  
  // Set modal title for updating
  const modalTitle = document.getElementById('note-modal-title');
  const saveButton = document.getElementById('save-note-button');
  const noteIdInput = document.getElementById('note_id');
  
  modalTitle.textContent = 'Update Note';
  saveButton.textContent = 'Update Note';
  noteIdInput.value = noteId;
  
  // Fetch the note data to pre-fill the form
  fetch(`/admin/patient_notes/${noteId}/edit`, {
    headers: {
      'Accept': 'application/json',
      'X-Requested-With': 'XMLHttpRequest'
    }
  })
  .then(response => response.json())
  .then(data => {
    console.log('Note data received:', data);
    
    // Pre-fill the form with the note data
    const titleInput = document.getElementById('patient_note_title');
    const textInput = document.getElementById('patient_note_text');
    const colorInput = document.getElementById('patient_note_color');
    
    if (titleInput) titleInput.value = data.title || '';
    if (textInput) textInput.value = data.text || '';
    if (colorInput) colorInput.value = data.color || '';
    
    // Select the correct color option
    const colorOptions = document.querySelectorAll('.note-color-option');
    colorOptions.forEach(opt => {
      opt.classList.remove('ring-2', 'ring-offset-2');
      if (opt.dataset.color === data.color) {
        opt.classList.add('ring-2', 'ring-offset-2');
      }
    });
  })
  .catch(error => {
    console.error('Error fetching note data:', error);
    toastr.error('Failed to load note data');
  });
  
  // Show the modal
  modal.classList.remove('hidden');
  document.body.classList.add('overflow-hidden');
};

// Helper function to select note color
window.selectNoteColor = function(element, color) {
  // Remove selected class from all options
  document.querySelectorAll('.note-color-option').forEach(opt => {
    opt.classList.remove('ring-2', 'ring-offset-2');
  });
  
  // Add selected class to clicked option
  if (element) {
    element.classList.add('ring-2', 'ring-offset-2');
  }
  
  // Set the color value in the hidden input
  const colorInput = document.getElementById('patient_note_color');
  if (colorInput) {
    colorInput.value = color;
  }
};

// Function to close the modal
window.closeNoteModal = function() {
  const modal = document.getElementById('patient-note-modal');
  if (modal) {
    modal.classList.add('hidden');
    document.body.classList.remove('overflow-hidden');
  }
};

// Handle form submission
window.handleNoteSubmit = function(event) {
  event.preventDefault();
  
  const form = event.target;
  const noteId = document.getElementById('note_id').value;
  const isUpdate = noteId && noteId.trim() !== '';
  
  // Show loading state on button
  const saveBtn = document.getElementById('save-note-button');
  saveBtn.disabled = true;
  saveBtn.innerHTML = `<span class="inline-block animate-spin mr-2">↻</span> ${isUpdate ? 'Updating' : 'Creating'}...`;
  
  // Submit the form via AJAX
  const formData = new FormData(form);
  
  // Determine the endpoint based on whether we're creating or updating
  const endpoint = isUpdate 
    ? `/admin/patient_notes/${noteId}` 
    : '/admin/patient_notes';
  
  const method = isUpdate ? 'PATCH' : 'POST';
  
  fetch(endpoint, {
    method: method,
    body: formData,
    headers: {
      'X-Requested-With': 'XMLHttpRequest'
    }
  })
  .then(response => response.json())
  .then(data => {
    console.log(`Note ${isUpdate ? 'updated' : 'created'} successfully:`, data);
    
    // Show toast notification instead of modal
    toastr.success(`Note ${isUpdate ? 'updated' : 'created'} successfully`);
    
    // Update the note in the UI if needed
    if (data.html) {
      if (isUpdate) {
        const noteElement = document.querySelector(`.note[data-note-id="${noteId}"]`);
        if (noteElement) {
          noteElement.outerHTML = data.html;
        }
      } else {
        // For new notes, append to the list
        const notesContainer = document.querySelector('.first-notes');
        if (notesContainer) {
          const headerElement = document.getElementById('notes_header');
          if (headerElement) {
            headerElement.insertAdjacentHTML('afterend', data.html);
          } else {
            notesContainer.insertAdjacentHTML('beforeend', data.html);
          }
        }
      }
    }
    
    // No need to close modal, just reload the page
    // This ensures the note appears in the list without showing a modal
    setTimeout(() => {
      window.location.reload();
    }, 500);
  })
  .catch(error => {
    console.error(`Error ${isUpdate ? 'updating' : 'creating'} note:`, error);
    toastr.error(`Failed to ${isUpdate ? 'update' : 'create'} note`);
  })
  .finally(() => {
    // Reset button state
    saveBtn.disabled = false;
    saveBtn.innerHTML = isUpdate ? 'Update Note' : 'Create Note';
  });
};

// Initialize note menu functionality
document.addEventListener('DOMContentLoaded', function() {
  // Toggle note menu visibility
  document.querySelectorAll('.note-menu-toggle').forEach(toggle => {
    toggle.addEventListener('click', function(e) {
      e.preventDefault();
      e.stopPropagation();
      
      const noteId = this.dataset.noteId;
      const menu = document.querySelector(`.note-menu[data-note-id="${noteId}"]`);
      
      if (menu) {
        menu.classList.toggle('hidden');
      }
    });
  });
  
  // Close note menus when clicking outside
  document.addEventListener('click', function(e) {
    // Don't close if clicking on a menu button or inside a menu
    if (e.target.closest('.note-menu-button') || e.target.closest('.note-menu')) {
      return;
    }

    const menus = document.querySelectorAll('.note-menu:not(.hidden)');
    menus.forEach(menu => {
      menu.classList.add('hidden');
    });
  });
});
