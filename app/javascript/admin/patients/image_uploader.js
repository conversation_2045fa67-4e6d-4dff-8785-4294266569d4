// Simple patient image uploader
// Auto-submit the form as soon as a user selects a file.

document.addEventListener('DOMContentLoaded', () => {
  console.log('Image uploader script loaded');
  const inputs = document.querySelectorAll('[data-patient-image-input]');
  console.log('Found', inputs.length, 'image inputs');

  inputs.forEach(input => {
    console.log('Setting up listener for input:', input);
    input.addEventListener('change', (e) => {
      console.log('File input changed:', e.target.files);
      const form = input.closest('form');
      if (form) {
        console.log('Submitting form:', form);
        form.submit();
      } else {
        console.log('No form found for input');
      }
    });
  });
});
