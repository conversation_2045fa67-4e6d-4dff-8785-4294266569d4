// Simple patient image uploader
// Auto-submit the form as soon as a user selects a file.

document.addEventListener('DOMContentLoaded', () => {
  // Handle file input changes
  document.querySelectorAll('[data-patient-image-input]').forEach(input => {
    input.addEventListener('change', () => {
      const form = input.closest('form');
      if (form) {
        form.submit();
      }
    });
  });

  // Handle clicks on the blue + and edit buttons
  document.querySelectorAll('[data-patient-image-trigger]').forEach(trigger => {
    trigger.addEventListener('click', (e) => {
      e.preventDefault();
      e.stopPropagation();

      // Find the associated file input
      const form = trigger.closest('form');
      if (form) {
        const fileInput = form.querySelector('[data-patient-image-input]');
        if (fileInput) {
          fileInput.click();
        }
      }
    });
  });

  // Also handle clicks on the avatar wrapper (the image itself)
  document.querySelectorAll('[data-avatar-wrapper]').forEach(wrapper => {
    wrapper.addEventListener('click', (e) => {
      // Only trigger if clicking on the image itself, not the button
      if (!e.target.closest('[data-patient-image-trigger]')) {
        e.preventDefault();
        e.stopPropagation();

        const form = wrapper.closest('form');
        if (form) {
          const fileInput = form.querySelector('[data-patient-image-input]');
          if (fileInput) {
            fileInput.click();
          }
        }
      }
    });
  });
});
